import React from 'react';
import { Helmet } from 'react-helmet-async';

import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationAmenities,
  TherapeuticInterventions,
  NearbyAmenities,
  ConditionsTreated,
  NearbyHospitals,
  OtherConsultingLocations,
  InsuranceAndFunding,
  PatientsPrivacy,
  LocationCTA
} from './index';

import { StandardPageLayout } from '@/components/layout';
import { StandardErrorBoundary } from '@/components/shared';
import { CompleteLocationData } from '@/types/location';

/**
 * Generic Location Template
 * Reusable template that can handle all location page variations
 * Preserves all content while eliminating code duplication
 */

export interface LocationSEOData {
  title: string;
  description: string;
  keywords: string;
  canonicalUrl: string;
}

export interface LocationSpecialSections {
  patientsPrivacy?: {
    title: string;
    subtitle: string;
    description1: string;
    description2: string;
    description3: string;
    drAliashkevichLink: string;
  };
}

export interface LocationTemplateProps {
  locationData: CompleteLocationData;
  specialSections?: LocationSpecialSections;
  seoData: LocationSEOData;
  layoutVariant?: 'standard' | 'split' | 'custom';
  customSections?: React.ReactNode[];
}

const GenericLocationTemplate: React.FC<LocationTemplateProps> = ({
  locationData,
  specialSections,
  seoData,
  layoutVariant = 'standard',
  customSections = []
}) => {
  const renderContactAndMapSection = () => {
    if (layoutVariant === 'split') {
      return (
        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Contact Information */}
              <div>
                <StandardErrorBoundary componentName="LocationContactInfo">
                  <LocationContactInfo
                    address={locationData.contact.address}
                    phone={locationData.contact.phone}
                    email={locationData.contact.email}
                    hours={locationData.contact.hours}
                    consultingHours={locationData.contact.consultingHours}
                    appointmentProcess={locationData.contact.appointmentProcess}
                  />
                </StandardErrorBoundary>
              </div>

              {/* Map and Getting Here */}
              <div>
                <StandardErrorBoundary componentName="LocationMap">
                  <LocationMap
                    embedUrl={locationData.map.embedUrl}
                    title={locationData.map.title}
                    transportOptions={locationData.map.transportOptions}
                    gettingHereTitle={locationData.map.gettingHereTitle}
                    publicTransportTitle={locationData.map.publicTransportTitle}
                    parkingTitle={locationData.map.parkingTitle}
                  />
                </StandardErrorBoundary>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Standard layout
    return (
      <section className="py-16">
        <div className="container">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="md:w-1/2">
              <div className="mb-8">
                <h2 className="text-enhanced-heading text-2xl font-bold mb-4">Location Details</h2>
                <p className="text-foreground/90">
                  Everything you need to know about our {locationData.contact.address.suburb} consulting location
                </p>
              </div>

              <StandardErrorBoundary componentName="LocationContactInfo">
                <LocationContactInfo
                  address={locationData.contact.address}
                  phone={locationData.contact.phone}
                  email={locationData.contact.email}
                  hours={locationData.contact.hours}
                  consultingHours={locationData.contact.consultingHours}
                  appointmentProcess={locationData.contact.appointmentProcess}
                />
              </StandardErrorBoundary>
            </div>

            <div className="md:w-1/2">
              <div className="mb-8">
                <h2 className="text-enhanced-heading text-2xl font-bold mb-4">Location Map</h2>
              </div>

              <StandardErrorBoundary componentName="LocationMap">
                <LocationMap
                  embedUrl={locationData.map.embedUrl}
                  title={locationData.map.title}
                  transportOptions={locationData.map.transportOptions}
                  gettingHereTitle={locationData.map.gettingHereTitle}
                  publicTransportTitle={locationData.map.publicTransportTitle}
                  parkingTitle={locationData.map.parkingTitle}
                />
              </StandardErrorBoundary>
            </div>
          </div>
        </div>
      </section>
    );
  };

  return (
    <>
      <Helmet>
        <title>{seoData.title}</title>
        <meta name="description" content={seoData.description} />
        <meta name="keywords" content={seoData.keywords} />
        <link rel="canonical" href={seoData.canonicalUrl} />
      </Helmet>

      <StandardErrorBoundary componentName="LocationTemplate" showErrorDetails={false}>
        <StandardPageLayout>
          {/* Hero Section */}
          <StandardErrorBoundary componentName="LocationHero">
            <LocationHero
              title={locationData.hero.title}
              subtitle={locationData.hero.subtitle}
              introduction1={locationData.hero.introduction1}
              introduction2={locationData.hero.introduction2}
              introduction3={locationData.hero.introduction3}
              imageUrl={locationData.hero.imageUrl}
            />
          </StandardErrorBoundary>

          {/* Contact and Map Section */}
          {renderContactAndMapSection()}

          {/* Therapeutic Interventions Section - Optional */}
          {locationData.therapeuticInterventions && (
            <StandardErrorBoundary componentName="TherapeuticInterventions">
              <TherapeuticInterventions
                title={locationData.therapeuticInterventions.title}
                subtitle={locationData.therapeuticInterventions.subtitle}
                description={locationData.therapeuticInterventions.description}
                externalLink={locationData.therapeuticInterventions.externalLink}
                interventions={locationData.therapeuticInterventions.interventions}
              />
            </StandardErrorBoundary>
          )}

          {/* Facilities Section */}
          <StandardErrorBoundary componentName="LocationFacilities">
            <LocationFacilities
              title={locationData.facilities.title}
              subtitle={locationData.facilities.subtitle}
              description={locationData.facilities.description}
              facilities={locationData.facilities.facilities}
              gallery={locationData.facilities.gallery}
            />
          </StandardErrorBoundary>

          {/* Amenities Section - Optional */}
          {locationData.amenities && (
            <StandardErrorBoundary componentName="LocationAmenities">
              <LocationAmenities
                title={locationData.amenities.title}
                description={locationData.amenities.description}
                locationDetails={locationData.amenities.locationDetails}
                medicalFacilities={locationData.amenities.medicalFacilities}
                surroundingAmenities={locationData.amenities.surroundingAmenities}
                transportation={locationData.amenities.transportation}
                parking={locationData.amenities.parking}
                images={locationData.amenities.images}
              />
            </StandardErrorBoundary>
          )}

          {/* Nearby Amenities Section */}
          <StandardErrorBoundary componentName="NearbyAmenities">
            <NearbyAmenities
              title={locationData.nearbyAmenities.title}
              subtitle={locationData.nearbyAmenities.subtitle}
              description={locationData.nearbyAmenities.description}
              categories={locationData.nearbyAmenities.categories}
            />
          </StandardErrorBoundary>

          {/* Conditions Treated Section - Optional */}
          {locationData.conditionsTreated && (
            <StandardErrorBoundary componentName="ConditionsTreated">
              <ConditionsTreated
                title={locationData.conditionsTreated.title}
                subtitle={locationData.conditionsTreated.subtitle}
                description={locationData.conditionsTreated.description}
                categories={locationData.conditionsTreated.categories}
              />
            </StandardErrorBoundary>
          )}

          {/* Nearby Hospitals Section */}
          <StandardErrorBoundary componentName="NearbyHospitals">
            <NearbyHospitals
              title={locationData.nearbyHospitals.title}
              subtitle={locationData.nearbyHospitals.subtitle}
              description={locationData.nearbyHospitals.description}
              hospitals={locationData.nearbyHospitals.hospitals}
            />
          </StandardErrorBoundary>

          {/* Other Consulting Locations Section */}
          <StandardErrorBoundary componentName="OtherConsultingLocations">
            <OtherConsultingLocations
              title={locationData.otherLocations?.title || locationData.otherConsultingLocations?.title}
              subtitle={locationData.otherLocations?.subtitle || locationData.otherConsultingLocations?.subtitle}
              description={locationData.otherLocations?.description || locationData.otherConsultingLocations?.description}
              locations={locationData.otherLocations?.locations || locationData.otherConsultingLocations?.locations}
            />
          </StandardErrorBoundary>

          {/* Insurance and Funding Section - Optional */}
          {locationData.insuranceAndFunding && (
            <StandardErrorBoundary componentName="InsuranceAndFunding">
              <InsuranceAndFunding
                title={locationData.insuranceAndFunding.title}
                subtitle={locationData.insuranceAndFunding.subtitle}
                categories={locationData.insuranceAndFunding.categories}
              />
            </StandardErrorBoundary>
          )}

          {/* Patients' Privacy Section - Optional */}
          {specialSections?.patientsPrivacy && (
            <StandardErrorBoundary componentName="PatientsPrivacy">
              <PatientsPrivacy
                title={specialSections.patientsPrivacy.title}
                subtitle={specialSections.patientsPrivacy.subtitle}
                description1={specialSections.patientsPrivacy.description1}
                description2={specialSections.patientsPrivacy.description2}
                description3={specialSections.patientsPrivacy.description3}
                drAliashkevichLink={specialSections.patientsPrivacy.drAliashkevichLink}
              />
            </StandardErrorBoundary>
          )}

          {/* Custom Sections */}
          {customSections.map((section, index) => (
            <StandardErrorBoundary key={index} componentName={`CustomSection${index}`}>
              {section}
            </StandardErrorBoundary>
          ))}

          {/* CTA Section */}
          <StandardErrorBoundary componentName="LocationCTA">
            <LocationCTA
              title={locationData.cta.title}
              description={locationData.cta.description}
              buttons={locationData.cta.buttons}
            />
          </StandardErrorBoundary>
        </StandardPageLayout>
      </StandardErrorBoundary>
    </>
  );
};

GenericLocationTemplate.displayName = 'GenericLocationTemplate';

export default GenericLocationTemplate;
