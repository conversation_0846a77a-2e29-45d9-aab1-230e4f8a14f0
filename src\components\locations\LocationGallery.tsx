import React from 'react';

interface GalleryImage {
  src: string;
  alt: string;
}

interface LocationGalleryProps {
  title: string;
  description: string;
  images: GalleryImage[];
}

/**
 * Location Gallery Component
 * Displays a grid of facility images with hover effects
 * Preserves all original gallery content from Surrey Hills location page
 */
const LocationGallery: React.FC<LocationGalleryProps> = ({
  title,
  description,
  images
}) => {
  return (
    <div className="mt-12">
      <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-center">{title}</h3>
      <p className="text-foreground/90 text-center mb-8">
        {description}
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {images.map((image, index) => (
          <div key={index} className="relative h-64 rounded-lg overflow-hidden shadow-md">
            <img
              src={image.src}
              alt={image.alt}
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

LocationGallery.displayName = 'LocationGallery';

export default LocationGallery;
