# Unified Color System Documentation

## Overview

This document outlines the comprehensive unified color system implemented across the neurosurgeon website. All hard-coded color values have been replaced with CSS custom properties (variables) to ensure consistency, maintainability, and proper theme support.

## Core Color Variables

### Base Theme Colors
```css
--background: 0 0% 100%;
--foreground: 215 25% 12%;
--card: 0 0% 100%;
--card-foreground: 215 25% 12%;
--primary: 210 85% 40%;
--primary-foreground: 0 0% 100%;
--secondary: 210 30% 96%;
--secondary-foreground: 215 25% 18%;
--muted: 210 25% 95%;
--muted-foreground: 215 25% 28%;
```

### Medical Professional Colors
```css
--medical-blue: 210 85% 40%;
--medical-blue-foreground: 0 0% 100%;
--medical-blue-light: 210 85% 97%;
--success: 142 65% 32%;
--warning: 38 85% 48%;
--error: 0 65% 45%;
--info: 199 80% 40%;
```

### Semantic Status Colors
```css
--status-emergency: 0 84% 60%;
--status-urgent: 25 95% 53%;
--status-routine: 142 76% 36%;
```

### Medical Category Colors
```css
--category-spine: 217 91% 60%;
--category-brain: 271 81% 56%;
--category-nerve: 45 93% 47%;
```

### Severity Indicators
```css
--severity-mild: 142 76% 36%;
--severity-moderate: 25 95% 53%;
--severity-severe: 0 84% 60%;
```

### Anatomical Region Colors
```css
--region-cervical: 217 91% 60%;
--region-thoracic: 271 81% 56%;
--region-lumbar: 45 93% 47%;
```

### Treatment Outcome Colors
```css
--outcome-positive: 142 76% 36%;
--outcome-neutral: 217 91% 60%;
--outcome-negative: 0 84% 60%;
```

## Usage Guidelines

### 1. Always Use CSS Variables
**✅ Correct:**
```css
background-color: hsl(var(--primary));
color: hsl(var(--primary-foreground));
box-shadow: 0 4px 12px hsl(var(--foreground) / 0.3);
```

**❌ Incorrect:**
```css
background-color: #2563eb;
color: #ffffff;
box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
```

### 2. Use Utility Classes When Available
**✅ Preferred:**
```html
<div class="bg-region-cervical text-region-cervical-foreground">
<div class="bg-severity-mild-light border-severity-mild">
```

### 3. Opacity with HSL
For transparency effects, use the HSL syntax with alpha:
```css
background: hsl(var(--primary) / 0.1);
border-color: hsl(var(--border) / 0.3);
```

## Available Utility Classes

### Region Classes
- `.bg-region-cervical`, `.bg-region-cervical-light`
- `.bg-region-thoracic`, `.bg-region-thoracic-light`
- `.bg-region-lumbar`, `.bg-region-lumbar-light`
- `.text-region-*-foreground`, `.border-region-*`

### Severity Classes
- `.bg-severity-mild`, `.bg-severity-mild-light`
- `.bg-severity-moderate`, `.bg-severity-moderate-light`
- `.bg-severity-severe`, `.bg-severity-severe-light`
- `.text-severity-*-foreground`, `.border-severity-*`

### Status Classes
- `.bg-status-emergency`, `.bg-status-emergency-light`
- `.bg-status-urgent`, `.bg-status-urgent-light`
- `.bg-status-routine`, `.bg-status-routine-light`
- `.text-status-*-foreground`, `.border-status-*`

### Category Classes
- `.bg-category-spine`, `.bg-category-spine-light`
- `.bg-category-brain`, `.bg-category-brain-light`
- `.bg-category-nerve`, `.bg-category-nerve-light`
- `.text-category-*-foreground`, `.border-category-*`

### Outcome Classes
- `.bg-outcome-positive`, `.bg-outcome-positive-light`
- `.bg-outcome-neutral`, `.bg-outcome-neutral-light`
- `.bg-outcome-negative`, `.bg-outcome-negative-light`
- `.text-outcome-*-foreground`, `.border-outcome-*`

## Dark Theme Support

All color variables automatically adapt to dark theme. The system includes:
- Enhanced contrast ratios for better readability
- Adjusted opacity values for dark backgrounds
- Professional medical color palette optimized for both themes

## Accessibility Compliance

- All color combinations meet WCAG AA contrast requirements
- Color variables are designed for optimal readability
- Semantic color usage ensures meaning is not conveyed by color alone

## Migration Complete

The following hard-coded values have been successfully replaced:

### Replaced in `src/index.css`:
- `rgba(59, 130, 246, 0.1)` → `hsl(var(--primary) / 0.1)`
- `rgba(59, 130, 246, 0.2)` → `hsl(var(--primary) / 0.2)`
- `rgba(0, 0, 0, 0.1)` → `hsl(var(--foreground) / 0.1)`
- `rgba(0, 0, 0, 0.25)` → `hsl(var(--foreground) / 0.25)`
- `rgba(0, 0, 0, 0.3)` → `hsl(var(--foreground) / 0.3)`
- `rgba(0, 0, 0, 0.35)` → `hsl(var(--foreground) / 0.35)`
- All text-shadow and box-shadow rgba values

### Replaced in `src/styles/responsive-utilities.css`:
- `rgba(0, 0, 0, 0.05)` → `hsl(var(--foreground) / 0.05)`
- `rgba(255, 255, 255, 0.1)` → `hsl(var(--background) / 0.1)`

## Maintenance Guidelines

1. **Never introduce new hard-coded colors**
2. **Always use existing CSS variables or create new ones following the naming convention**
3. **Test color changes in both light and dark themes**
4. **Ensure accessibility compliance for any new color combinations**
5. **Update this documentation when adding new color variables**

## Benefits Achieved

✅ **Consistency**: All colors follow a unified system
✅ **Maintainability**: Easy to update colors globally
✅ **Theme Support**: Automatic dark/light theme adaptation
✅ **Accessibility**: WCAG AA compliant color combinations
✅ **Performance**: Reduced CSS bundle size through variable reuse
✅ **Developer Experience**: Clear semantic color naming

This unified color system ensures a professional, consistent, and accessible user experience across the entire neurosurgeon website.