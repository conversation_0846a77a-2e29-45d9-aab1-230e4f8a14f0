import { LucideIcon } from 'lucide-react';
import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface FormField {
  name: string;
  label: string;
  type: string;
  placeholder?: string;
  required: boolean;
  options?: { value: string; label: string }[];
}

interface ContactForm {
  id: string;
  title: string;
  description: string;
  icon: LucideIcon;
  fields: FormField[];
}

interface ContactFormsSectionProps {
  title: string;
  subtitle: string;
  forms: ContactForm[];
}

const ContactFormsSection: React.FC<ContactFormsSectionProps> = ({
  title,
  subtitle,
  forms
}) => {
  const deviceInfo = useDeviceDetection();
  const [activeForm, setActiveForm] = useState(forms[0]?.id || '');

  const renderField = (field: FormField, formId: string) => {
    const fieldId = `${formId}-${field.name}`;

    switch (field.type) {
      case 'select':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldId}>
              {field.label}
              {field.required && <span className="text-foreground ml-1">*</span>}
            </Label>
            <Select>
              <SelectTrigger id={fieldId}>
                <SelectValue placeholder={field.placeholder || `Select ${field.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'textarea':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldId}>
              {field.label}
              {field.required && <span className="text-foreground ml-1">*</span>}
            </Label>
            <Textarea 
              id={fieldId} 
              placeholder={field.placeholder}
              className="min-h-[100px]"
            />
          </div>
        );

      case 'date':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldId}>
              {field.label}
              {field.required && <span className="text-foreground ml-1">*</span>}
            </Label>
            <Input 
              id={fieldId} 
              type="date"
              placeholder={field.placeholder}
            />
          </div>
        );

      default:
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldId}>
              {field.label}
              {field.required && <span className="text-foreground ml-1">*</span>}
            </Label>
            <Input 
              id={fieldId} 
              type={field.type} 
              placeholder={field.placeholder}
            />
          </div>
        );
    }
  };

  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-foreground/90 max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        <Tabs value={activeForm} onValueChange={setActiveForm} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-8",
            deviceInfo.isMobile 
              ? "grid-cols-2 h-auto" 
              : "grid-cols-4"
          )}>
            {forms.map((form) => (
              <TabsTrigger 
                key={form.id} 
                value={form.id} 
                className={cn(
                  "text-center",
                  deviceInfo.isMobile ? "py-3 px-2 text-xs" : "py-3"
                )}
              >
                <div className="flex items-center gap-2">
                  <form.icon className="h-4 w-4" />
                  <span className={deviceInfo.isMobile ? "hidden sm:inline" : ""}>
                    {form.title}
                  </span>
                </div>
              </TabsTrigger>
            ))}
          </TabsList>

          {forms.map((form) => (
            <TabsContent key={form.id} value={form.id} className="space-y-6">
              <Card className="shadow-lg">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <form.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{form.title}</CardTitle>
                      <p className="text-foreground/90">{form.description}</p>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <form className="space-y-6">
                    <div className={cn(
                      "grid gap-4",
                      deviceInfo.isMobile 
                        ? "grid-cols-1" 
                        : "grid-cols-1 md:grid-cols-2"
                    )}>
                      {form.fields.slice(0, Math.ceil(form.fields.length / 2)).map(field => 
                        renderField(field, form.id)
                      )}
                    </div>
                    
                    <div className={cn(
                      "grid gap-4",
                      deviceInfo.isMobile 
                        ? "grid-cols-1" 
                        : "grid-cols-1 md:grid-cols-2"
                    )}>
                      {form.fields.slice(Math.ceil(form.fields.length / 2)).map(field => 
                        renderField(field, form.id)
                      )}
                    </div>

                    <div className="flex justify-end pt-4">
                      <Button type="submit" className="px-8">
                        Submit {form.title}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Form Submission Note */}
        <div className="mt-8 text-center">
          <Card className="max-w-2xl mx-auto bg-info-light/30 border-info/30">
            <CardContent className="p-6">
              <h3 className="font-bold text-foreground mb-2">
                📧 Form Submission
              </h3>
              <p className="text-foreground text-sm">
                All forms are securely transmitted and we aim to respond within 24 hours during business days. 
                For urgent matters, please call (03) 9008 4200 directly.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ContactFormsSection;
