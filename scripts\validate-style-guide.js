#!/usr/bin/env node

/**
 * Style Guide Validation Script
 * 
 * This script helps enforce the Golden Rule:
 * - User-facing content: British English
 * - Code elements: American English
 * 
 * Usage: node scripts/validate-style-guide.js
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

// Common British vs American English patterns
const STYLE_PATTERNS = {
  // User-facing content should use British English
  userFacing: {
    // These should be British English in user-facing content
    britishRequired: [
      { american: /\bcolor\b/g, british: 'colour', context: 'user-facing text' },
      { american: /\bcenter\b/g, british: 'centre', context: 'user-facing text' },
      { american: /\borganization\b/g, british: 'organisation', context: 'user-facing text' },
      { american: /\bspecialize\b/g, british: 'specialise', context: 'user-facing text' },
      { american: /\brealize\b/g, british: 'realise', context: 'user-facing text' },
      { american: /\bbehavior\b/g, british: 'behaviour', context: 'user-facing text' },
      { american: /\bfavor\b/g, british: 'favour', context: 'user-facing text' },
      { american: /\bhonor\b/g, british: 'honour', context: 'user-facing text' },
      { american: /\blabor\b/g, british: 'labour', context: 'user-facing text' },
      { american: /\bneighbor\b/g, british: 'neighbour', context: 'user-facing text' }
    ]
  },
  
  // Code elements should use American English
  code: {
    // These should be American English in code
    americanRequired: [
      { british: /\bcolour\b/g, american: 'color', context: 'code elements' },
      { british: /\bcentre\b/g, american: 'center', context: 'code elements' },
      { british: /\borganisation\b/g, american: 'organization', context: 'code elements' },
      { british: /\bspecialise\b/g, american: 'specialize', context: 'code elements' },
      { british: /\brealise\b/g, american: 'realize', context: 'code elements' },
      { british: /\bbehaviour\b/g, american: 'behavior', context: 'code elements' },
      { british: /\bfavour\b/g, american: 'favor', context: 'code elements' },
      { british: /\bhonour\b/g, american: 'honor', context: 'code elements' },
      { british: /\blabour\b/g, american: 'labor', context: 'code elements' },
      { british: /\bneighbour\b/g, american: 'neighbor', context: 'code elements' }
    ]
  }
};

// File patterns for different contexts
const FILE_CONTEXTS = {
  userFacing: {
    patterns: [
      'src/components/**/*.tsx',
      'src/pages/**/*.tsx',
      'public/data/**/*.json',
      'docs/**/*.md'
    ],
    // Look for user-facing content patterns
    contentPatterns: [
      /["'`]([^"'`]*)["'`]/g, // String literals
      /title\s*=\s*["'`]([^"'`]*)["'`]/g, // Title attributes
      /alt\s*=\s*["'`]([^"'`]*)["'`]/g, // Alt text
      /placeholder\s*=\s*["'`]([^"'`]*)["'`]/g, // Placeholders
      /aria-label\s*=\s*["'`]([^"'`]*)["'`]/g, // ARIA labels
      /<h[1-6][^>]*>([^<]*)<\/h[1-6]>/g, // Headings
      /<p[^>]*>([^<]*)<\/p>/g, // Paragraphs
      /<span[^>]*>([^<]*)<\/span>/g, // Spans
      /<div[^>]*>([^<]*)<\/div>/g // Divs with text content
    ]
  },
  
  code: {
    patterns: [
      'src/**/*.ts',
      'src/**/*.tsx',
      'src/**/*.js',
      'src/**/*.jsx'
    ],
    // Look for code element patterns
    codePatterns: [
      /\b[a-zA-Z][a-zA-Z0-9]*\s*:/g, // Object properties
      /\bconst\s+([a-zA-Z][a-zA-Z0-9]*)\s*=/g, // Variable declarations
      /\blet\s+([a-zA-Z][a-zA-Z0-9]*)\s*=/g, // Variable declarations
      /\bvar\s+([a-zA-Z][a-zA-Z0-9]*)\s*=/g, // Variable declarations
      /\bfunction\s+([a-zA-Z][a-zA-Z0-9]*)\s*\(/g, // Function declarations
      /\b([a-zA-Z][a-zA-Z0-9]*)\s*\(/g, // Function calls
      /\bclass\s+([a-zA-Z][a-zA-Z0-9]*)\s*{/g, // Class declarations
      /\binterface\s+([a-zA-Z][a-zA-Z0-9]*)\s*{/g, // Interface declarations
      /\btype\s+([a-zA-Z][a-zA-Z0-9]*)\s*=/g, // Type declarations
      /className\s*=\s*["'`]([^"'`]*)["'`]/g // CSS class names
    ]
  }
};

class StyleGuideValidator {
  constructor() {
    this.violations = [];
    this.warnings = [];
  }

  async validate() {
    console.log('🔍 Validating Style Guide compliance...');
    console.log('📋 Golden Rule: British English (user-facing) vs American English (code)');
    console.log('');

    // Validate user-facing content
    await this.validateUserFacingContent();
    
    // Validate code elements
    await this.validateCodeElements();
    
    // Report results
    this.reportResults();
    
    return this.violations.length === 0;
  }

  async validateUserFacingContent() {
    console.log('📝 Checking user-facing content for British English...');
    
    const files = this.getFiles(FILE_CONTEXTS.userFacing.patterns);
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      this.checkUserFacingContent(file, content);
    }
  }

  async validateCodeElements() {
    console.log('💻 Checking code elements for American English...');
    
    const files = this.getFiles(FILE_CONTEXTS.code.patterns);
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      this.checkCodeElements(file, content);
    }
  }

  checkUserFacingContent(file, content) {
    // Extract user-facing content
    const userContent = this.extractUserFacingContent(content);
    
    // Check for American English in user-facing content
    STYLE_PATTERNS.userFacing.britishRequired.forEach(pattern => {
      const matches = userContent.match(pattern.american);
      if (matches) {
        matches.forEach(match => {
          this.violations.push({
            file,
            type: 'user-facing-american-english',
            message: `Found "${match}" in user-facing content. Should be "${pattern.british}" (British English)`,
            line: this.getLineNumber(content, match),
            suggestion: `Replace "${match}" with "${pattern.british}"`
          });
        });
      }
    });
  }

  checkCodeElements(file, content) {
    // Extract code elements
    const codeElements = this.extractCodeElements(content);
    
    // Check for British English in code elements
    STYLE_PATTERNS.code.americanRequired.forEach(pattern => {
      const matches = codeElements.match(pattern.british);
      if (matches) {
        matches.forEach(match => {
          // Skip if it's in a comment or string literal that might be user-facing
          if (!this.isInUserFacingContext(content, match)) {
            this.violations.push({
              file,
              type: 'code-british-english',
              message: `Found "${match}" in code elements. Should be "${pattern.american}" (American English)`,
              line: this.getLineNumber(content, match),
              suggestion: `Replace "${match}" with "${pattern.american}"`
            });
          }
        });
      }
    });
  }

  extractUserFacingContent(content) {
    let userContent = '';
    
    FILE_CONTEXTS.userFacing.contentPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        userContent += matches.join(' ') + ' ';
      }
    });
    
    return userContent;
  }

  extractCodeElements(content) {
    let codeElements = '';
    
    FILE_CONTEXTS.code.codePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        codeElements += matches.join(' ') + ' ';
      }
    });
    
    return codeElements;
  }

  isInUserFacingContext(content, match) {
    // Check if the match is within quotes (likely user-facing)
    const index = content.indexOf(match);
    const before = content.substring(Math.max(0, index - 50), index);
    const after = content.substring(index, Math.min(content.length, index + 50));
    
    // Simple heuristic: if surrounded by quotes, likely user-facing
    return /["'`]/.test(before) && /["'`]/.test(after);
  }

  getLineNumber(content, match) {
    const index = content.indexOf(match);
    if (index === -1) return 1;
    
    return content.substring(0, index).split('\n').length;
  }

  getFiles(patterns) {
    let files = [];
    
    patterns.forEach(pattern => {
      const matches = glob.sync(pattern, { ignore: ['node_modules/**', 'dist/**', '.git/**'] });
      files = files.concat(matches);
    });
    
    return [...new Set(files)]; // Remove duplicates
  }

  reportResults() {
    console.log('');
    console.log('📊 Style Guide Validation Results');
    console.log('================================');
    
    if (this.violations.length === 0) {
      console.log('✅ No style guide violations found!');
      console.log('🎉 All content follows the Golden Rule correctly.');
    } else {
      console.log(`❌ Found ${this.violations.length} style guide violations:`);
      console.log('');
      
      this.violations.forEach((violation, index) => {
        console.log(`${index + 1}. ${violation.file}:${violation.line}`);
        console.log(`   ${violation.message}`);
        console.log(`   💡 ${violation.suggestion}`);
        console.log('');
      });
      
      console.log('📋 Remember the Golden Rule:');
      console.log('   • User-facing content: British English (colour, centre, specialise)');
      console.log('   • Code elements: American English (backgroundColor, centerAlign)');
      console.log('');
      console.log('📖 See docs/STYLE_GUIDE.md for complete guidelines.');
    }
    
    if (this.warnings.length > 0) {
      console.log('');
      console.log(`⚠️  ${this.warnings.length} warnings:`);
      this.warnings.forEach(warning => {
        console.log(`   ${warning}`);
      });
    }
  }
}

// Run validation if called directly
const validator = new StyleGuideValidator();

validator.validate().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Validation failed:', error);
  process.exit(1);
});

export default StyleGuideValidator;