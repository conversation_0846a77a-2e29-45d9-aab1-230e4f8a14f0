#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Critical images that need to be downloaded
const criticalBrainImages = [
  {
    name: 'brain-anatomy-detailed.jpg',
    url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'Detailed brain anatomy - used in 15+ components',
    directory: 'brain-conditions'
  },
  {
    name: 'brain-tumour-hero.jpg',
    url: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'Brain tumor hero section background',
    directory: 'brain-conditions'
  },
  {
    name: 'hydrocephalus-hero.jpg',
    url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Hydrocephalus condition hero image',
    directory: 'brain-conditions'
  },
  {
    name: 'hydrocephalus-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Hydrocephalus brain anatomy illustration',
    directory: 'brain-conditions'
  }
];

const criticalNeurologicalImages = [
  {
    name: 'cerebral-aneurysm-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Cerebral aneurysm anatomy diagram',
    directory: 'neurological-conditions'
  },
  {
    name: 'circle-of-willis-diagram.jpg',
    url: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Circle of Willis vascular anatomy',
    directory: 'neurological-conditions'
  },
  {
    name: 'trigeminal-nerve-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Trigeminal nerve anatomical diagram',
    directory: 'neurological-conditions'
  }
];

const criticalPeripheralImages = [
  {
    name: 'default-nerve-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Default peripheral nerve anatomy',
    directory: 'peripheral-nerve-conditions'
  },
  {
    name: 'carpal-tunnel-syndrome-hero.jpg',
    url: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Carpal tunnel syndrome hero image',
    directory: 'peripheral-nerve-conditions'
  },
  {
    name: 'ulnar-neuropathy-hero.jpg',
    url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Ulnar neuropathy condition hero',
    directory: 'peripheral-nerve-conditions'
  }
];

const allCriticalImages = [
  ...criticalBrainImages,
  ...criticalNeurologicalImages,
  ...criticalPeripheralImages
];

function createDirectories() {
  console.log('Creating directories...');
  const baseDir = path.join(__dirname, '..', 'public', 'images');
  const directories = ['brain-conditions', 'neurological-conditions', 'peripheral-nerve-conditions'];
  
  directories.forEach(dir => {
    const fullPath = path.join(baseDir, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`Created directory: ${fullPath}`);
    } else {
      console.log(`Directory already exists: ${fullPath}`);
    }
  });
}

function downloadImage(imageInfo) {
  return new Promise((resolve, reject) => {
    const baseDir = path.join(__dirname, '..', 'public', 'images');
    const filePath = path.join(baseDir, imageInfo.directory, imageInfo.name);
    
    console.log(`Downloading ${imageInfo.name}...`);
    
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`File already exists: ${imageInfo.name}`);
      resolve();
      return;
    }
    
    const file = fs.createWriteStream(filePath);
    
    https.get(imageInfo.url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${imageInfo.name}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Successfully downloaded: ${imageInfo.name}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

async function downloadAllCriticalImages() {
  console.log('Starting download of all critical images...');
  console.log(`Total images to download: ${allCriticalImages.length}`);
  
  try {
    createDirectories();
    
    for (let i = 0; i < allCriticalImages.length; i++) {
      const imageInfo = allCriticalImages[i];
      console.log(`\nProcessing ${i + 1}/${allCriticalImages.length}: ${imageInfo.name}`);
      
      try {
        await downloadImage(imageInfo);
      } catch (error) {
        console.error(`Failed to download ${imageInfo.name}:`, error.message);
      }
      
      // Add delay between downloads to be respectful
      if (i < allCriticalImages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log('\n✅ Download process completed!');
  } catch (error) {
    console.error('❌ Error during download process:', error);
  }
}

// Always execute when run directly - simplified approach
console.log('Script started - checking execution context...');
console.log('import.meta.url:', import.meta.url);
console.log('process.argv[1]:', process.argv[1]);

// Execute immediately
downloadAllCriticalImages().catch(console.error);

export { downloadAllCriticalImages, allCriticalImages };