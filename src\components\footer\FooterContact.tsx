import { Mail, Phone, MapPin } from 'lucide-react';
import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { ContactInfo } from '@/data/footer/footerData';
import { cn } from '@/lib/utils';

interface FooterContactProps {
  title: string;
  contactInfo: ContactInfo;
  animationDelay?: number;
}

const FooterContact: React.FC<FooterContactProps> = ({ 
  title, 
  contactInfo, 
  animationDelay = 300 
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section 
      aria-labelledby="contact-heading" 
      className={cn(
        deviceInfo.isMobile 
          ? "mobile-fade-in" 
          : `animate-fade-in [animation-delay:${animationDelay}ms]`
      )}
    >
      <h4 
        id="contact-heading" 
        className={cn(
          "font-bold mb-mobile-md",
          deviceInfo.isMobile ? "mobile-subheading" : "text-xl mb-4"
        )}
      >
        {title}
      </h4>
      <address 
        className={cn(
          deviceInfo.isMobile ? "space-y-mobile-md" : "space-y-3"
        )} 
        style={{ fontStyle: 'normal' }}
      >
        {/* Address */}
        <div className="flex items-start">
          <MapPin className={cn(
            "text-primary mr-mobile-sm mt-0.5",
            deviceInfo.isMobile ? "w-5 h-5" : "w-5 h-5 mr-2"
          )} aria-hidden="true" />
          <span className={cn(
            "text-foreground/90",
            deviceInfo.isMobile ? "mobile-text" : ""
          )}>
            {contactInfo.address.street}<br />
            {contactInfo.address.city} {contactInfo.address.state} {contactInfo.address.postcode}<br />
            {contactInfo.address.country}
          </span>
        </div>

        {/* Phone */}
        <div className="flex items-center">
          <Phone className={cn(
            "text-primary mr-mobile-sm",
            deviceInfo.isMobile ? "w-5 h-5" : "w-5 h-5 mr-2"
          )} aria-hidden="true" />
          <a
            href={contactInfo.phone.href}
            aria-label={contactInfo.phone.ariaLabel}
            className={cn(
              "text-foreground/90 transition-colors touch-feedback",
              deviceInfo.isMobile
                ? "mobile-text"
                : "hover:text-primary"
            )}
          >
            {contactInfo.phone.number}
          </a>
        </div>

        {/* Email */}
        <div className="flex items-center">
          <Mail className={cn(
            "text-primary mr-mobile-sm",
            deviceInfo.isMobile ? "w-5 h-5" : "w-5 h-5 mr-2"
          )} aria-hidden="true" />
          <a
            href={contactInfo.email.href}
            aria-label={contactInfo.email.ariaLabel}
            className={cn(
              "text-foreground/90 transition-colors touch-feedback",
              deviceInfo.isMobile
                ? "mobile-text"
                : "hover:text-primary"
            )}
          >
            {contactInfo.email.address}
          </a>
        </div>
      </address>
    </section>
  );
};

FooterContact.displayName = 'FooterContact';

export default FooterContact;
