import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface DoctorProfileSectionProps {
  doctorProfile: {
    title: string;
    description: string;
    qualifications: string[];
    experience: string;
    specializations: string[];
    affiliations: string[];
  };
}

const DoctorProfileSection: React.FC<DoctorProfileSectionProps> = ({ doctorProfile }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-muted/20",
      deviceInfo.isMobile ? "py-12" : "py-20"
    )}>
      <div className={cn(
        "container max-w-6xl mx-auto",
        deviceInfo.isMobile ? "px-4" : "px-6"
      )}>
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold text-foreground mb-6",
            deviceInfo.isMobile ? "text-2xl" : "text-4xl"
          )}>
            {doctorProfile.title}
          </h2>
          <div className={cn(
            "max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-base" : "text-xl"
          )}>
            <p className="text-foreground/90 leading-relaxed">
              {doctorProfile.description}
            </p>
          </div>
        </div>

        <div className={cn(
          "grid gap-8 mb-12",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
        )}>
          {/* Qualifications */}
          <div className="bg-card border border-border rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
            <h3 className={cn(
              "font-bold text-primary mb-6",
              deviceInfo.isMobile ? "text-xl" : "text-2xl"
            )}>
              Qualifications & Certifications
            </h3>
            <ul className="space-y-3">
              {doctorProfile.qualifications.map((qualification, index) => (
                <li key={index} className={cn(
                  "text-foreground/90 leading-relaxed flex items-start",
                  deviceInfo.isMobile ? "text-sm" : "text-base"
                )}>
                  <span className="text-primary mr-3 mt-1">•</span>
                  <span>{qualification}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Specializations */}
          <div className="bg-card border border-border rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
            <h3 className={cn(
              "font-bold text-primary mb-6",
              deviceInfo.isMobile ? "text-xl" : "text-2xl"
            )}>
              Areas of Specialization
            </h3>
            <ul className="space-y-3">
              {doctorProfile.specializations.map((specialization, index) => (
                <li key={index} className={cn(
                  "text-foreground/90 leading-relaxed flex items-start",
                  deviceInfo.isMobile ? "text-sm" : "text-base"
                )}>
                  <span className="text-primary mr-3 mt-1">•</span>
                  <span>{specialization}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Experience and Affiliations */}
        <div className="text-center">
          <div className="bg-card border border-border rounded-xl p-8 shadow-lg">
            <div className={cn(
              "font-bold text-primary mb-8",
              deviceInfo.isMobile ? "text-lg" : "text-xl"
            )}>
              {doctorProfile.experience}
            </div>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 md:grid-cols-4"
            )}>
              {doctorProfile.affiliations.map((affiliation, index) => (
                <div key={index} className={cn(
                  "text-foreground/90 font-medium text-center leading-relaxed",
                  deviceInfo.isMobile ? "text-sm" : "text-base"
                )}>
                  {affiliation}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

DoctorProfileSection.displayName = 'DoctorProfileSection';

export default DoctorProfileSection;
