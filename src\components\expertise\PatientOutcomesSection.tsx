import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface PatientOutcomesSectionProps {
  patientOutcomes: {
    title: string;
    description: string;
    metrics: {
      successRates: {
        title: string;
        description: string;
        stats: {
          label: string;
          value: string;
        }[];
      };
      recoveryInfo: {
        title: string;
        description: string;
        timelines: {
          procedure: string;
          timeline: string;
        }[];
      };
    };
  };
}

const PatientOutcomesSection: React.FC<PatientOutcomesSectionProps> = ({ patientOutcomes }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-primary/5",
      deviceInfo.isMobile ? "mobile-section" : "py-16"
    )}>
      <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className={cn(
            "font-bold mb-mobile-md",
            deviceInfo.isMobile ? "mobile-heading" : "text-3xl mb-6"
          )}>
            {patientOutcomes.title}
          </h2>
          <p className={cn(
            "text-foreground/90",
            deviceInfo.isMobile ? "mobile-text" : ""
          )}>
            {patientOutcomes.description}
          </p>
        </div>

        <div className={cn(
          "grid gap-mobile-lg",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2 gap-12"
        )}>
          {/* Success Rates */}
          <div className="card p-8 rounded-lg shadow-md medical-card">
            <h3 className={cn(
              "font-semibold mb-mobile-sm text-primary",
              deviceInfo.isMobile ? "mobile-subheading" : "text-2xl mb-4"
            )}>
              {patientOutcomes.metrics.successRates.title}
            </h3>
            <p className={cn(
              "text-foreground/90 mb-mobile-md",
              deviceInfo.isMobile ? "mobile-text" : "mb-6"
            )}>
              {patientOutcomes.metrics.successRates.description}
            </p>
            <div className={cn(
              "grid gap-mobile-sm",
              deviceInfo.isMobile ? "grid-cols-2" : "grid-cols-2 gap-4"
            )}>
              {patientOutcomes.metrics.successRates.stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className={cn(
                    "font-bold text-primary mb-mobile-xs",
                    deviceInfo.isMobile ? "mobile-subheading" : "text-3xl mb-2"
                  )}>
                    {stat.value}
                  </div>
                  <div className={cn(
                    "text-foreground/90",
                    deviceInfo.isMobile ? "mobile-text" : "text-sm"
                  )}>
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recovery Information */}
          <div className="card p-8 rounded-lg shadow-md medical-card">
            <h3 className={cn(
              "font-semibold mb-mobile-sm text-primary",
              deviceInfo.isMobile ? "mobile-subheading" : "text-2xl mb-4"
            )}>
              {patientOutcomes.metrics.recoveryInfo.title}
            </h3>
            <p className={cn(
              "text-foreground/90 mb-mobile-md",
              deviceInfo.isMobile ? "mobile-text" : "mb-6"
            )}>
              {patientOutcomes.metrics.recoveryInfo.description}
            </p>
            <div className="space-y-4">
              {patientOutcomes.metrics.recoveryInfo.timelines.map((timeline, index) => (
                <div key={index} className="border-l-4 border-primary pl-4">
                  <div className={cn(
                    "font-semibold text-primary",
                    deviceInfo.isMobile ? "mobile-text" : ""
                  )}>
                    {timeline.procedure}
                  </div>
                  <div className={cn(
                    "text-foreground/90",
                    deviceInfo.isMobile ? "mobile-text" : "text-sm"
                  )}>
                    {timeline.timeline}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

PatientOutcomesSection.displayName = 'PatientOutcomesSection';

export default PatientOutcomesSection;
