import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface ConsultingLocation {
  title: string;
  description: string;
  address: string;
  phone: string;
  viewDetailsText: string;
  viewDetailsLink: string;
  image: {
    src: string;
    alt: string;
  };
}

interface OtherConsultingLocationsProps {
  title: string;
  subtitle: string;
  description: string;
  locations: ConsultingLocation[];
}

/**
 * Other Consulting Locations Component
 * Displays alternative consulting locations for <PERSON><PERSON>
 * Preserves all original consulting locations content from Surrey Hills location page
 */
const OtherConsultingLocations: React.FC<OtherConsultingLocationsProps> = ({
  title,
  subtitle,
  description,
  locations
}) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-foreground/90">
            {subtitle}
          </p>
        </div>

        <div className="mt-8 max-w-3xl mx-auto mb-12">
          <p className="text-enhanced-body text-center">
            {description}
          </p>
        </div>

        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-8">
          {locations.map((location, index) => (
            <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
              <div className="relative h-48 rounded-lg overflow-hidden shadow-md mb-6">
                <img
                  src={location.image.src}
                  alt={location.image.alt}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
              </div>

              <h3 className="text-xl font-semibold mb-3 text-primary">{location.title}</h3>
              <p className="text-foreground/90 mb-4">
                {location.description}
              </p>
              <p className="text-foreground/90 mb-4">
                <span className="font-medium">Address:</span> {location.address}<br />
                <span className="font-medium">Phone:</span> {location.phone}
              </p>
              <Button asChild variant="outline" className="w-full">
                <Link to={location.viewDetailsLink} className="text-primary hover:">{location.viewDetailsText}</Link>
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

OtherConsultingLocations.displayName = 'OtherConsultingLocations';

export default OtherConsultingLocations;
