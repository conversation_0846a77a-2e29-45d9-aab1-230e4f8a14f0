#!/usr/bin/env node

/**
 * FIX WARNING-LEVEL COLOR ISSUES
 * 
 * This script fixes all WARNING-level color issues identified by the professional color verification:
 * - border-error/30 → border-border/70
 * - bg-error-light/30 → bg-muted/50
 * - bg-error-light/20 → bg-muted/30
 * - border-error/50 → border-border
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔧 FIXING WARNING-LEVEL COLOR ISSUES...\n');

// WARNING COLOR REPLACEMENTS
const WARNING_COLOR_FIXES = [
  {
    pattern: /border-error\/30/g,
    replacement: 'border-border/70',
    description: 'border-error/30 → border-border/70'
  },
  {
    pattern: /bg-error-light\/30/g,
    replacement: 'bg-muted/50',
    description: 'bg-error-light/30 → bg-muted/50'
  },
  {
    pattern: /bg-error-light\/20/g,
    replacement: 'bg-muted/30',
    description: 'bg-error-light/20 → bg-muted/30'
  },
  {
    pattern: /border-error\/50/g,
    replacement: 'border-border',
    description: 'border-error/50 → border-border'
  }
];

/**
 * Get all TypeScript, JavaScript, and CSS files in the project
 */
function getAllFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules, .git, dist, and other build directories
        if (!['node_modules', '.git', 'dist', '.next', 'build', '.vite', 'scripts'].includes(item)) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && /\.(tsx?|jsx?|css|scss|sass|less)$/.test(item)) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(PROJECT_ROOT);
  return files;
}

/**
 * Fix warning colors in a single file
 */
function fixFileColors(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  let updatedContent = content;
  let hasChanges = false;
  const appliedFixes = [];

  // Apply each color fix
  for (const fix of WARNING_COLOR_FIXES) {
    const matches = updatedContent.match(fix.pattern);
    if (matches) {
      updatedContent = updatedContent.replace(fix.pattern, fix.replacement);
      appliedFixes.push({
        description: fix.description,
        count: matches.length
      });
      hasChanges = true;
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    return appliedFixes;
  }

  return null;
}

/**
 * Main execution
 */
function main() {
  try {
    const files = getAllFiles();
    let totalFiles = 0;
    let totalFixes = 0;

    console.log(`📁 Scanning ${files.length} files for warning-level color issues...\n`);

    for (const file of files) {
      const fixes = fixFileColors(file);
      if (fixes) {
        const relativePath = path.relative(PROJECT_ROOT, file);
        const fileFixCount = fixes.reduce((sum, fix) => sum + fix.count, 0);
        
        console.log(`✅ ${relativePath} (${fileFixCount} fixes)`);
        fixes.forEach(fix => {
          console.log(`   ${fix.description}: ${fix.count} instances`);
        });
        console.log('');
        
        totalFiles++;
        totalFixes += fileFixCount;
      }
    }

    console.log('📊 WARNING COLOR FIXES COMPLETE!');
    console.log(`📁 Files updated: ${totalFiles}`);
    console.log(`🔧 Total fixes applied: ${totalFixes}`);
    
    if (totalFixes === 0) {
      console.log('\n✅ NO WARNING-LEVEL COLOR ISSUES FOUND!');
      console.log('🎉 All warning colors are already professional.');
    } else {
      console.log('\n🎉 ALL WARNING-LEVEL COLOR ISSUES FIXED!');
      console.log('✅ Replaced unprofessional colors with medical-appropriate alternatives.');
      console.log('🔍 Professional appearance significantly improved.');
    }

  } catch (error) {
    console.error('❌ Error during warning color fixes:', error);
    process.exit(1);
  }
}

// Run the script
main();