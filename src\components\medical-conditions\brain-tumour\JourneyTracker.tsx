import { CheckCircle, Circle, Calendar, FileText, Activity } from 'lucide-react';
import React, { useState, useEffect } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface JourneyStep {
  id: string;
  title: string;
  description: string;
  category: 'diagnosis' | 'treatment' | 'recovery' | 'follow-up';
  estimatedDuration: string;
  isCompleted: boolean;
  isActive: boolean;
  tasks: Array<{
    id: string;
    title: string;
    description: string;
    isCompleted: boolean;
    isOptional: boolean;
  }>;
}

const initialJourneySteps: JourneyStep[] = [
  {
    id: 'initial-symptoms',
    title: 'Initial Symptoms & Consultation',
    description: 'First awareness of symptoms and initial medical consultation',
    category: 'diagnosis',
    estimatedDuration: '1-2 weeks',
    isCompleted: false,
    isActive: true,
    tasks: [
      { id: 'symptom-tracking', title: 'Track symptoms', description: 'Keep a diary of symptoms and their frequency', isCompleted: false, isOptional: false },
      { id: 'gp-visit', title: 'Visit GP', description: 'Initial consultation with general practitioner', isCompleted: false, isOptional: false },
      { id: 'referral', title: 'Get specialist referral', description: 'Obtain referral to neurologist or neurosurgeon', isCompleted: false, isOptional: false }
    ]
  },
  {
    id: 'diagnostic-imaging',
    title: 'Diagnostic Imaging & Tests',
    description: 'Comprehensive imaging and diagnostic procedures',
    category: 'diagnosis',
    estimatedDuration: '2-4 weeks',
    isCompleted: false,
    isActive: false,
    tasks: [
      { id: 'mri-scan', title: 'MRI Brain Scan', description: 'Detailed brain imaging with contrast', isCompleted: false, isOptional: false },
      { id: 'additional-tests', title: 'Additional tests', description: 'Blood tests, neurological examination', isCompleted: false, isOptional: true },
      { id: 'biopsy', title: 'Tissue biopsy', description: 'If required for definitive diagnosis', isCompleted: false, isOptional: true }
    ]
  },
  {
    id: 'diagnosis-planning',
    title: 'Diagnosis & Treatment Planning',
    description: 'Receiving diagnosis and developing treatment plan',
    category: 'diagnosis',
    estimatedDuration: '1-2 weeks',
    isCompleted: false,
    isActive: false,
    tasks: [
      { id: 'diagnosis-discussion', title: 'Diagnosis discussion', description: 'Meet with specialist to discuss results', isCompleted: false, isOptional: false },
      { id: 'treatment-options', title: 'Review treatment options', description: 'Understand all available treatment approaches', isCompleted: false, isOptional: false },
      { id: 'second-opinion', title: 'Seek second opinion', description: 'Consider additional specialist consultation', isCompleted: false, isOptional: true }
    ]
  },
  {
    id: 'treatment-phase',
    title: 'Active Treatment',
    description: 'Undergoing primary treatment (surgery, radiation, chemotherapy)',
    category: 'treatment',
    estimatedDuration: '4-12 weeks',
    isCompleted: false,
    isActive: false,
    tasks: [
      { id: 'pre-treatment', title: 'Pre-treatment preparation', description: 'Medical clearance, consent, planning', isCompleted: false, isOptional: false },
      { id: 'primary-treatment', title: 'Primary treatment', description: 'Surgery, radiation, or other primary intervention', isCompleted: false, isOptional: false },
      { id: 'adjuvant-treatment', title: 'Adjuvant treatment', description: 'Additional treatments if recommended', isCompleted: false, isOptional: true }
    ]
  },
  {
    id: 'recovery-rehabilitation',
    title: 'Recovery & Rehabilitation',
    description: 'Post-treatment recovery and rehabilitation services',
    category: 'recovery',
    estimatedDuration: '6-12 weeks',
    isCompleted: false,
    isActive: false,
    tasks: [
      { id: 'immediate-recovery', title: 'Immediate recovery', description: 'Hospital stay and initial healing', isCompleted: false, isOptional: false },
      { id: 'rehabilitation', title: 'Rehabilitation therapy', description: 'Physical, occupational, or speech therapy', isCompleted: false, isOptional: true },
      { id: 'return-activities', title: 'Return to activities', description: 'Gradual return to normal activities', isCompleted: false, isOptional: false }
    ]
  },
  {
    id: 'long-term-follow-up',
    title: 'Long-term Follow-up',
    description: 'Ongoing monitoring and surveillance',
    category: 'follow-up',
    estimatedDuration: 'Ongoing',
    isCompleted: false,
    isActive: false,
    tasks: [
      { id: 'regular-scans', title: 'Regular MRI scans', description: 'Scheduled imaging for monitoring', isCompleted: false, isOptional: false },
      { id: 'clinic-visits', title: 'Clinic appointments', description: 'Regular check-ups with medical team', isCompleted: false, isOptional: false },
      { id: 'support-groups', title: 'Support groups', description: 'Participate in patient support networks', isCompleted: false, isOptional: true }
    ]
  }
];

export function JourneyTracker() {
  const deviceInfo = useDeviceDetection();
  const [journeySteps, setJourneySteps] = useState<JourneyStep[]>(initialJourneySteps);
  const [activeTab, setActiveTab] = useState('overview');

  // Load saved progress from localStorage
  useEffect(() => {
    const savedProgress = localStorage.getItem('brain-tumour-journey');
    if (savedProgress) {
      try {
        const parsed = JSON.parse(savedProgress);
        setJourneySteps(parsed);
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error('Error loading saved journey progress:', error);
        }
      }
    }
  }, []);

  // Save progress to localStorage
  useEffect(() => {
    localStorage.setItem('brain-tumour-journey', JSON.stringify(journeySteps));
  }, [journeySteps]);

  const toggleTask = (stepId: string, taskId: string) => {
    setJourneySteps(prev => prev.map(step => {
      if (step.id === stepId) {
        const updatedTasks = step.tasks.map(task => 
          task.id === taskId ? { ...task, isCompleted: !task.isCompleted } : task
        );
        const completedRequiredTasks = updatedTasks.filter(t => !t.isOptional && t.isCompleted).length;
        const totalRequiredTasks = updatedTasks.filter(t => !t.isOptional).length;
        const stepCompleted = completedRequiredTasks === totalRequiredTasks;
        
        return { ...step, tasks: updatedTasks, isCompleted: stepCompleted };
      }
      return step;
    }));
  };

  const getOverallProgress = () => {
    const completedSteps = journeySteps.filter(step => step.isCompleted).length;
    return (completedSteps / journeySteps.length) * 100;
  };

  const getCategoryProgress = (category: string) => {
    const categorySteps = journeySteps.filter(step => step.category === category);
    const completedSteps = categorySteps.filter(step => step.isCompleted).length;
    return categorySteps.length > 0 ? (completedSteps / categorySteps.length) * 100 : 0;
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'diagnosis': return FileText;
      case 'treatment': return Activity;
      case 'recovery': return Users;
      case 'follow-up': return Calendar;
      default: return Circle;
    }
  };

  const categories = [
    { id: 'diagnosis', name: 'Diagnosis', colour: 'info' },
    { id: 'treatment', name: 'Treatment', colour: 'success' },
    { id: 'recovery', name: 'Recovery', colour: 'primary' },
    { id: 'follow-up', name: 'Follow-up', colour: 'secondary' }
  ];

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container max-w-6xl">
        <div className="text-center mb-12">
          <h2 className={cn("font-bold mb-4", deviceInfo.isMobile ? "text-2xl" : "text-3xl")}>
            Your Brain Tumour Journey Tracker
          </h2>
          <p className={cn("text-foreground/90 max-w-3xl mx-auto", deviceInfo.isMobile ? "text-sm" : "text-lg")}>
            Track your progress through diagnosis, treatment, and recovery. This tool helps you stay organised and informed throughout your journey.
          </p>
        </div>

        {/* Overall Progress */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" />
              Overall Progress
            </CardTitle>
            <CardDescription>
              {Math.round(getOverallProgress())}% of your journey milestones completed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={getOverallProgress()} className="h-3" />
          </CardContent>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className={cn("grid w-full mb-8", deviceInfo.isMobile ? "grid-cols-2" : "grid-cols-3")}>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            {!deviceInfo.isMobile && <TabsTrigger value="categories">By Category</TabsTrigger>}
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Category Progress Cards */}
            <div className={cn("grid gap-4", deviceInfo.isMobile ? "grid-cols-2" : "grid-cols-4")}>
              {categories.map((category) => {
                const Icon = getCategoryIcon(category.id);
                const progress = getCategoryProgress(category.id);
                return (
                  <Card key={category.id}>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon className="h-4 w-4 text-primary" />
                        <span className="font-medium text-sm">{category.name}</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                      <p className="text-xs text-foreground/95 mt-1">
                        {Math.round(progress)}% complete
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Current Active Steps */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Current Steps</h3>
              <div className="space-y-4">
                {journeySteps.filter(step => step.isActive || !step.isCompleted).slice(0, 2).map((step) => (
                  <Card key={step.id} className={step.isActive ? "border-primary" : ""}>
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        {step.isCompleted ? (
                          <CheckCircle className="h-5 w-5 text-success" />
                        ) : (
                          <Circle className="h-5 w-5 text-foreground/90" />
                        )}
                        <CardTitle className="text-lg">{step.title}</CardTitle>
                        <Badge variant={step.isActive ? "default" : "secondary"}>
                          {step.estimatedDuration}
                        </Badge>
                      </div>
                      <CardDescription>{step.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {step.tasks.map((task) => (
                          <div key={task.id} className="flex items-start gap-3">
                            <Checkbox
                              checked={task.isCompleted}
                              onCheckedChange={() => toggleTask(step.id, task.id)}
                              className="mt-1"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <span className={cn("font-medium", task.isCompleted && "line-through text-foreground/90")}>
                                  {task.title}
                                </span>
                                {task.isOptional && (
                                  <Badge variant="outline" className="text-xs">Optional</Badge>
                                )}
                              </div>
                              <p className="text-sm text-foreground/95">{task.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="timeline" className="space-y-4">
            {journeySteps.map((step, _index) => (
              <div key={step.id} className="flex gap-4">
                <div className="flex flex-col items-center">
                  <div className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center",
                    step.isCompleted ? "bg-success text-primary-foreground" : 
                    step.isActive ? "bg-primary text-primary-foreground" : "bg-muted text-foreground/90"
                  )}>
                    {step.isCompleted ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  {index < journeySteps.length - 1 && (
                    <div className="w-0.5 h-16 bg-muted mt-2" />
                  )}
                </div>
                <Card className="flex-1">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{step.title}</CardTitle>
                      <Badge variant="outline">{step.estimatedDuration}</Badge>
                    </div>
                    <CardDescription>{step.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {step.tasks.map((task) => (
                        <div key={task.id} className="flex items-center gap-2 text-sm">
                          <Checkbox
                            checked={task.isCompleted}
                            onCheckedChange={() => toggleTask(step.id, task.id)}
                            size="sm"
                          />
                          <span className={task.isCompleted ? "line-through text-foreground/90" : ""}>
                            {task.title}
                          </span>
                          {task.isOptional && (
                            <Badge variant="outline" className="text-xs">Optional</Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </TabsContent>

          <TabsContent value="categories" className="space-y-6">
            {categories.map((category) => {
              const categorySteps = journeySteps.filter(step => step.category === category.id);
              const Icon = getCategoryIcon(category.id);
              
              return (
                <Card key={category.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Icon className="h-5 w-5 text-primary" />
                      {category.name} Phase
                    </CardTitle>
                    <CardDescription>
                      {getCategoryProgress(category.id).toFixed(0)}% complete
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {categorySteps.map((step) => (
                        <div key={step.id} className="border rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            {step.isCompleted ? (
                              <CheckCircle className="h-4 w-4 text-success" />
                            ) : (
                              <Circle className="h-4 w-4 text-foreground/90" />
                            )}
                            <span className="font-medium">{step.title}</span>
                            <Badge variant="outline" className="text-xs">{step.estimatedDuration}</Badge>
                          </div>
                          <p className="text-sm text-foreground/95 mb-3">{step.description}</p>
                          <div className="space-y-2">
                            {step.tasks.map((task) => (
                              <div key={task.id} className="flex items-center gap-2 text-sm">
                                <Checkbox
                                  checked={task.isCompleted}
                                  onCheckedChange={() => toggleTask(step.id, task.id)}
                                  size="sm"
                                />
                                <span className={task.isCompleted ? "line-through text-foreground/90" : ""}>
                                  {task.title}
                                </span>
                                {task.isOptional && (
                                  <Badge variant="outline" className="text-xs">Optional</Badge>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className={cn("flex gap-3 mt-8", deviceInfo.isMobile ? "flex-col" : "justify-center")}>
          <Button size="lg" >
            <Calendar className="mr-2 h-4 w-4" />
            Schedule Next Appointment
          </Button>
          <Button variant="outline" size="lg" >
            <FileText className="mr-2 h-4 w-4" />
            Download Progress Report
          </Button>
        </div>

        {/* Disclaimer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-foreground/95 max-w-2xl mx-auto">
            This tracker is a general guide and your actual journey may vary. Always follow your healthcare team's specific recommendations and timeline.
          </p>
        </div>
      </div>
    </section>
  );
}

export default JourneyTracker;
