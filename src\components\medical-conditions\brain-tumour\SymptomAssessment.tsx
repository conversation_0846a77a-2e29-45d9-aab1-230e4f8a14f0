import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Target, ArrowRight } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Symptom {
  name: string;
  description: string;
  severity: 'mild' | 'moderate' | 'severe';
}

interface SymptomCategory {
  category: string;
  icon: unknown;
  symptoms: Symptom[];
}

interface SymptomAssessmentProps {
  symptomCategories: SymptomCategory[];
}

interface AssessmentResult {
  totalSymptoms: number;
  severeSymptoms: number;
  moderateSymptoms: number;
  mildSymptoms: number;
  recommendation: string;
  urgency: 'low' | 'medium' | 'high';
}

export function SymptomAssessment({ symptomCategories }: SymptomAssessmentProps) {
  const deviceInfo = useDeviceDetection();
  const [selectedSymptoms, setSelectedSymptoms] = useState<Set<string>>(new Set());
  const [_showResults, _setShowResults] = useState(false);
  const assessmentId = useId();
  const _resultsId = useId();

  const allSymptoms = symptomCategories.flatMap(category => 
    category.symptoms.map(symptom => ({
      ...symptom,
      category: category.category,
      id: `${category.category}-${symptom.name}`
    }))
  );

  const handleSymptomToggle = (symptomId: string) => {
    const newSelected = new Set(selectedSymptoms);
    if (newSelected.has(symptomId)) {
      newSelected.delete(symptomId);
    } else {
      newSelected.add(symptomId);
    }
    setSelectedSymptoms(newSelected);
  };

  const calculateResults = (): AssessmentResult => {
    const selectedSymptomDetails = allSymptoms.filter(symptom => 
      selectedSymptoms.has(symptom.id)
    );

    const severeCount = selectedSymptomDetails.filter(s => s.severity === 'severe').length;
    const moderateCount = selectedSymptomDetails.filter(s => s.severity === 'moderate').length;
    const mildCount = selectedSymptomDetails.filter(s => s.severity === 'mild').length;

    let recommendation = '';
    let urgency: 'low' | 'medium' | 'high' = 'low';

    if (severeCount > 0) {
      recommendation = 'You have reported severe symptoms that require immediate medical attention. Please contact your healthcare provider or seek emergency care.';
      urgency = 'high';
    } else if (moderateCount >= 2 || (moderateCount >= 1 && mildCount >= 2)) {
      recommendation = 'You have reported multiple concerning symptoms. We recommend scheduling an appointment with a healthcare provider for evaluation.';
      urgency = 'medium';
    } else if (moderateCount === 1 || mildCount >= 3) {
      recommendation = 'You have reported some symptoms that may warrant medical evaluation. Consider discussing these with your healthcare provider.';
      urgency = 'medium';
    } else {
      recommendation = 'Based on your responses, your symptoms appear to be mild. Continue monitoring and consult a healthcare provider if symptoms worsen or persist.';
      urgency = 'low';
    }

    return {
      totalSymptoms: selectedSymptomDetails.length,
      severeSymptoms: severeCount,
      moderateSymptoms: moderateCount,
      mildSymptoms: mildCount,
      recommendation,
      urgency
    };
  };

  const results = calculateResults();
  const progressValue = (selectedSymptoms.size / allSymptoms.length) * 100;

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'badge-emergency';
      case 'medium': return 'badge-info';
      default: return 'badge-success';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'high': return <AlertTriangle className="h-5 w-5" />;
      case 'medium': return <Target className="h-5 w-5" />;
      default: return <CheckCircle className="h-5 w-5" />;
    }
  };

  return (
    <section
      className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${assessmentId}-title`}
      role="region"
    >
      <div className="container">
        <div className="text-center mb-12">
          <h2
            id={`${assessmentId}-title`}
            className={cn(
              "font-bold mb-4",
              deviceInfo.isMobile ? "text-2xl" : "text-3xl"
            )}
          >
            Brain Tumour Symptom Assessment
          </h2>
          <p
            className={cn(
              "text-foreground/90 max-w-3xl mx-auto",
              deviceInfo.isMobile ? "text-sm" : "text-lg"
            )}
            id={`${assessmentId}-description`}
          >
            This assessment tool helps you identify potential brain tumour symptoms.
            Select any symptoms you're currently experiencing for personalised guidance.
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Assessment Progress</span>
            <span className="text-sm text-foreground/95">
              {selectedSymptoms.size} of {allSymptoms.length} symptoms selected
            </span>
          </div>
          <Progress value={progressValue} className="h-2" />
        </div>

        {/* Symptom Categories */}
        <div className="space-y-8 mb-8">
          {symptomCategories.map((category, categoryIndex) => (
            <Card key={categoryIndex}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <category.icon className="h-5 w-5 text-primary" />
                  {category.category}
                </CardTitle>
                <CardDescription>
                  Select any symptoms you're currently experiencing in this category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className={cn(
                  "grid gap-4",
                  deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
                )}>
                  {category.symptoms.map((symptom, symptomIndex) => {
                    const symptomId = `${category.category}-${symptom.name}`;
                    const isSelected = selectedSymptoms.has(symptomId);
                    
                    return (
                      <div
                        key={symptomIndex}
                        className={cn(
                          "flex items-start space-x-3 p-3 rounded-lg border transition-colors cursor-pointer",
                          isSelected ? "bg-primary/5 border-primary/20" : "hover:bg-muted"
                        )}
                        onClick={() => handleSymptomToggle(symptomId)}
                      >
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleSymptomToggle(symptomId)}
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">{symptom.name}</span>
                            <Badge 
                              variant={symptom.severity === 'severe' ? 'muted' : 
                                     symptom.severity === 'moderate' ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {symptom.severity}
                            </Badge>
                          </div>
                          <p className="text-xs text-foreground/95">{symptom.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Results Section */}
        {selectedSymptoms.size > 0 && (
          <Card className={cn("border-2", getUrgencyColor(results.urgency))}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getUrgencyIcon(results.urgency)}
                Assessment Results
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Summary Stats */}
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-2" : "grid-cols-4"
              )}>
                <div className="text-center">
                  <div className="text-enhanced-heading text-2xl font-bold text-primary">{results.totalSymptoms}</div>
                  <div className="text-sm text-foreground/95">Total Symptoms</div>
                </div>
                <div className="text-center">
                  <div className="text-enhanced-heading text-2xl font-bold text-foreground">{results.severeSymptoms}</div>
                  <div className="text-sm text-foreground/95">Severe</div>
                </div>
                <div className="text-center">
                  <div className="text-enhanced-heading text-2xl font-bold text-info">{results.moderateSymptoms}</div>
                  <div className="text-sm text-foreground/95">Moderate</div>
                </div>
                <div className="text-center">
                  <div className="text-enhanced-heading text-2xl font-bold text-success">{results.mildSymptoms}</div>
                  <div className="text-sm text-foreground/95">Mild</div>
                </div>
              </div>

              {/* Recommendation */}
              <div className="p-4 rounded-lg bg-background/50">
                <h4 className="font-semibold mb-2">Recommendation:</h4>
                <p className="text-sm">{results.recommendation}</p>
              </div>

              {/* Action Buttons */}
              <div className={cn(
                "flex gap-3",
                deviceInfo.isMobile ? "flex-col" : "flex-row"
              )}>
                {results.urgency === 'high' && (
                  <Button variant="default" size={deviceInfo.isMobile ? "default" : "lg"} >
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    Seek Emergency Care
                  </Button>
                )}
                <Button size={deviceInfo.isMobile ? "default" : "lg"} >
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Schedule Consultation
                </Button>
                <Button variant="outline" size={deviceInfo.isMobile ? "default" : "lg"} >
                  Learn More About Symptoms
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Disclaimer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-foreground/95 max-w-2xl mx-auto">
            This assessment tool is for educational purposes only and does not replace professional medical diagnosis. 
            Always consult with a qualified healthcare provider for proper evaluation and treatment of your symptoms.
          </p>
        </div>
      </div>
    </section>
  );
}

export default SymptomAssessment;
