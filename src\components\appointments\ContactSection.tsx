import { LucideIcon } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ContactMethod {
  type: string;
  icon: LucideIcon;
  value: string;
  description: string;
  hours?: string;
}

interface ContactSectionProps {
  title: string;
  subtitle: string;
  methods: ContactMethod[];
  emergencyNote: string;
  responseTime: string;
}

const ContactSection: React.FC<ContactSectionProps> = ({
  title,
  subtitle,
  methods,
  emergencyNote,
  responseTime
}) => {
  const deviceInfo = useDeviceDetection();

  const getContactAction = (method: ContactMethod) => {
    switch (method.type) {
      case 'Phone':
        return `tel:${method.value.replace(/\s/g, '')}`;
      case 'Email':
        return `mailto:${method.value}`;
      default:
        return '#';
    }
  };

  const isClickable = (type: string) => {
    return type === 'Phone' || type === 'Email';
  };

  return (
    <section className="py-16 bg-muted">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-foreground/90 max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        <div className={cn(
          "grid gap-6 mb-12",
          deviceInfo.isMobile 
            ? "grid-cols-1" 
            : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
        )}>
          {methods.map((method, index) => (
            <Card key={index} className="shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-3">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <method.icon className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <CardTitle className="text-lg">{method.type}</CardTitle>
              </CardHeader>
              
              <CardContent className="text-center">
                <div className="space-y-3">
                  {isClickable(method.type) ? (
                    <Button
                      asChild
                      variant="outline"
                      className="w-full h-auto py-3 px-4"
                    >
                      <a href={getContactAction(method)}>
                        <div>
                          <p className="font-semibold text-primary">
                            {method.value}
                          </p>
                          <p className="text-xs text-foreground/95 mt-1">
                            Click to {method.type.toLowerCase()}
                          </p>
                        </div>
                      </a>
                    </Button>
                  ) : (
                    <div className="p-3 bg-muted/50 rounded-lg">
                      <p className="font-semibold">{method.value}</p>
                    </div>
                  )}
                  
                  <p className="text-sm text-foreground/95">
                    {method.description}
                  </p>
                  
                  {method.hours && (
                    <Badge variant="secondary" className="text-xs">
                      {method.hours}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Important Notes */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Emergency Note */}
          <Card className="bg-muted/50 border-border">
            <CardHeader>
              <CardTitle className="text-foreground flex items-center gap-2">
                🚨 Emergency Contact
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground font-medium">
                {emergencyNote}
              </p>
            </CardContent>
          </Card>

          {/* Response Time */}
          <Card className="bg-info/10 border-info/50">
            <CardHeader>
              <CardTitle className="text-foreground flex items-center gap-2">
                ⏱️ Response Time
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground">
                {responseTime}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Office Location */}
        <div className="mt-12">
          <Card className="max-w-2xl mx-auto shadow-xl">
            <CardHeader className="text-center">
              <CardTitle className="text-xl text-primary">
                Main Office Location
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <div className="space-y-4">
                <div>
                  <h3 className="font-bold text-lg">miNEURO Consulting Suites</h3>
                  <p className="text-foreground/90">
                    Suite 4, Ground Floor<br />
                    619 Canterbury Road<br />
                    Surrey Hills VIC 3127
                  </p>
                </div>
                
                <div className="flex flex-wrap justify-center gap-2">
                  <Badge variant="outline">2 min walk from train station</Badge>
                  <Badge variant="outline">Wheelchair accessible</Badge>
                  <Badge variant="outline">Parking available</Badge>
                </div>
                
                <Button asChild className="mt-4">
                  <a
                    href="https://maps.google.com/?q=619+Canterbury+Road+Surrey+Hills+VIC+3127"
                    target="_blank"
                    rel="noopener noreferrer"
                    
                  >
                    View on Google Maps
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
