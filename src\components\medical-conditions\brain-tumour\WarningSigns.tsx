import { Alert<PERSON><PERSON><PERSON>, <PERSON>, Clock, Zap } from 'lucide-react';
import React, { useId } from 'react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface _WarningSign {
  sign: string;
  description: string;
  action: string;
}

interface WarningSignsProps {
  title: string;
  description: string;
  urgentSigns: WarningSign[];
}

export function WarningSigns({ title, description, urgentSigns }: WarningSignsProps) {
  const deviceInfo = useDeviceDetection();
  const warningId = useId();
  const emergencyNumber = "000"; // Australian emergency number

  return (
    <section
      className={cn("section-spacing section-background-emergency", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${warningId}-title`}
      role="region"
      aria-describedby={`${warningId}-description`}
    >
      <div className="container">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <AlertTriangle className="h-8 w-8 text-foreground" aria-hidden="true" />
            <h2
              id={`${warningId}-title`}
              className={cn(
                "text-enhanced-heading font-bold text-foreground",
                deviceInfo.isMobile ? "text-2xl" : "text-3xl"
              )}
            >
              {title}
            </h2>
          </div>
          <p
            id={`${warningId}-description`}
            className={cn(
              "text-enhanced-body text-foreground max-w-3xl mx-auto",
              deviceInfo.isMobile ? "text-sm" : "text-lg"
            )}
          >
            {description}
          </p>
        </div>

        {/* Emergency Alert */}
        <Alert className="mb-8 border-border bg-muted/50 dark:border-border/70/30 dark:bg-muted/20">
          <AlertTriangle className="h-5 w-5 text-foreground" />
          <AlertTitle className="text-foreground font-bold">Medical Emergency</AlertTitle>
          <AlertDescription className="text-foreground font-medium">
            If you experience any of these symptoms, seek immediate medical attention.
            Call <strong className="font-bold">{emergencyNumber}</strong> or go to the nearest emergency department.
          </AlertDescription>
        </Alert>

        {/* Warning Signs Grid */}
        <div className={cn(
          "grid gap-6 mb-8",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
        )}>
          {urgentSigns.map((sign, index) => (
            <Card key={index} className="border-border/70 hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-start gap-3 text-enhanced-heading text-foreground">
                  <div className="flex-shrink-0 w-8 h-8 bg-muted-light rounded-full flex items-center justify-center">
                    <Zap className="h-4 w-4 text-foreground" />
                  </div>
                  <span className={deviceInfo.isMobile ? "text-lg" : "text-xl"}>
                    {sign.sign}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-enhanced-body text-foreground mb-4">
                  {sign.description}
                </CardDescription>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-foreground" />
                  <Badge variant="muted" className="badge-emergency">
                    {sign.action}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Emergency Action Steps */}
        <Card className="border-border/70 bg-muted-light">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-2 text-foreground">
              <Phone className="h-5 w-5" />
              Emergency Action Steps
            </CardTitle>
            <CardDescription className="text-enhanced-body text-foreground">
              Follow these steps if you or someone you know experiences warning signs:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-4",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-muted rounded-full flex items-center justify-center font-bold">
                  1
                </div>
                <div>
                  <h4 className="text-enhanced-heading font-semibold text-foreground mb-1">Call Emergency Services</h4>
                  <p className="text-enhanced-body text-sm text-foreground">
                    Dial {emergencyNumber} immediately. Don't wait to see if symptoms improve.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-muted rounded-full flex items-center justify-center font-bold">
                  2
                </div>
                <div>
                  <h4 className="text-enhanced-heading font-semibold text-foreground mb-1">Stay Calm and Safe</h4>
                  <p className="text-enhanced-body text-sm text-foreground">
                    Keep the person comfortable and safe. Don't leave them alone.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-muted rounded-full flex items-center justify-center font-bold">
                  3
                </div>
                <div>
                  <h4 className="text-enhanced-heading font-semibold text-foreground mb-1">Provide Information</h4>
                  <p className="text-enhanced-body text-sm text-foreground">
                    Tell emergency responders about brain tumour history and current symptoms.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Resources */}
        <div className="text-center mt-8">
          <Card className="bg-info-light/30 border border-info/30">
            <CardContent className="pt-6">
              <h3 className="text-enhanced-heading font-semibold mb-2 text-foreground">Non-Emergency Concerns?</h3>
              <p className="text-enhanced-body text-foreground mb-4 text-sm">
                For non-urgent questions about your brain tumour or treatment, contact your healthcare team during regular hours.
              </p>
              <div className={cn(
                "flex gap-3",
                deviceInfo.isMobile ? "flex-col" : "flex-row justify-center"
              )}>
                <Button variant="outline" size={deviceInfo.isMobile ? "default" : "sm"} >
                  Contact Your Doctor
                </Button>
                <Button variant="outline" size={deviceInfo.isMobile ? "default" : "sm"} >
                  Nurse Helpline
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Disclaimer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-foreground/95 max-w-2xl mx-auto">
            This information is for educational purposes only and does not replace professional medical advice. 
            Always consult with your healthcare provider for personalised medical guidance. In case of emergency, 
            call {emergencyNumber} immediately.
          </p>
        </div>
      </div>
    </section>
  );
}

export default WarningSigns;
