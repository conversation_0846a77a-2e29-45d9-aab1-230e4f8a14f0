import React from 'react';

interface Feature {
  id: string;
  title: string;
  description: string;
  icon?: React.ReactNode;
  link?: string;
}

interface FeatureGridProps {
  title?: string;
  subtitle?: string;
  features: Feature[];
  columns?: 2 | 3 | 4;
  className?: string;
}

const FeatureGrid: React.FC<FeatureGridProps> = ({
  title,
  subtitle,
  features,
  columns = 3,
  className = ''
}) => {
  const getGridClass = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2';
      case 3:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
    }
  };

  return (
    <section className={`py-16 ${className}`}>
      <div className="container">
        {(title || subtitle) && (
          <div className="text-center max-w-3xl mx-auto mb-12">
            {title && <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>}
            {subtitle && <p className="text-foreground/90 text-lg">{subtitle}</p>}
          </div>
        )}

        <div className={`grid ${getGridClass()} gap-8`}>
          {features.map((feature) => (
            <div 
              key={feature.id} 
              className={`text-center p-6 rounded-lg ${
                feature.link ? 'hover:bg-muted/50 transition-colors cursor-pointer' : ''
              }`}
            >
              {feature.icon && (
                <div className="flex justify-center mb-4">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                    {feature.icon}
                  </div>
                </div>
              )}
              
              <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
              <p className="text-foreground/90">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureGrid;
