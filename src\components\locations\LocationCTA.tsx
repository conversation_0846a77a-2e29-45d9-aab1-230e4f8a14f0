import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface CTAButton {
  text: string;
  link: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'muted';
}

interface LocationCTAProps {
  title: string;
  description: string;
  buttons: CTAButton[];
}

const LocationCTA: React.FC<LocationCTAProps> = ({
  title,
  description,
  buttons
}) => {
  return (
    <section className="py-16 bg-background">
      <div className="container">
        <div className="text-center">
          <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{title}</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto text-foreground/90">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            {buttons.map((button, index) => (
              <Button
                key={index}
                asChild
                size="lg"
                variant={button.variant || 'default'}
                className="font-semibold"
              >
                <Link to={button.link} >
                  {button.text}
                </Link>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationCTA;
