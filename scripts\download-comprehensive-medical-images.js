#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Comprehensive brain condition images with diverse sources
const brainConditionImages = [
  {
    name: 'brain-anatomy-detailed.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by <PERSON><PERSON> on Unsplash',
    description: 'Detailed brain anatomy - primary reference',
    directory: 'brain-conditions'
  },
  {
    name: 'brain-tumour-hero.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Brain tumor medical imaging',
    directory: 'brain-conditions'
  },
  {
    name: 'brain-tumour-mri-scan.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'MRI brain scan showing tumor',
    directory: 'brain-conditions'
  },
  {
    name: 'brain-anatomy-tumour-location.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Brain anatomy showing tumor locations',
    directory: 'brain-conditions'
  },
  {
    name: 'hydrocephalus-hero.jpg',
    url: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    attribution: 'Photo by Jesse Orrico on Unsplash',
    description: 'Brain imaging for hydrocephalus',
    directory: 'brain-conditions'
  },
  {
    name: 'hydrocephalus-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1606672896853-d4f138e8e7b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Milad Fakurian on Unsplash',
    description: 'Brain ventricle anatomy for hydrocephalus',
    directory: 'brain-conditions'
  }
];

// Neurological condition images
const neurologicalImages = [
  {
    name: 'cerebral-aneurysm-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Cerebral vascular anatomy',
    directory: 'neurological-conditions'
  },
  {
    name: 'circle-of-willis-diagram.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Circle of Willis vascular diagram',
    directory: 'neurological-conditions'
  },
  {
    name: 'trigeminal-nerve-diagram.jpg',
    url: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Jesse Orrico on Unsplash',
    description: 'Trigeminal nerve anatomical diagram',
    directory: 'neurological-conditions'
  }
];

// Peripheral nerve condition images
const peripheralNerveImages = [
  {
    name: 'carpal-tunnel-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Carpal tunnel anatomical structure',
    directory: 'peripheral-nerve-conditions'
  },
  {
    name: 'carpal-tunnel-cross-section.jpg',
    url: 'https://images.unsplash.com/photo-1606672896853-d4f138e8e7b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Milad Fakurian on Unsplash',
    description: 'Carpal tunnel cross-sectional view',
    directory: 'peripheral-nerve-conditions'
  },
  {
    name: 'median-nerve-pathway.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Median nerve pathway anatomy',
    directory: 'peripheral-nerve-conditions'
  },
  {
    name: 'compression-mechanism.jpg',
    url: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Jesse Orrico on Unsplash',
    description: 'Nerve compression mechanism',
    directory: 'peripheral-nerve-conditions'
  },
  {
    name: 'peroneal-nerve-palsy.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Peroneal nerve anatomy',
    directory: 'peripheral-nerve-conditions'
  },
  {
    name: 'default-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1606672896853-d4f138e8e7b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Milad Fakurian on Unsplash',
    description: 'Default peripheral nerve anatomy',
    directory: 'peripheral-nerve-conditions'
  }
];

// Spine anatomy images
const spineAnatomyImages = [
  {
    name: 'spinal-canal.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Spinal canal anatomy',
    directory: 'spine-anatomy'
  },
  {
    name: 'vertebrae.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Vertebrae structure',
    directory: 'spine-anatomy'
  },
  {
    name: 'nerve-root-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Jesse Orrico on Unsplash',
    description: 'Spinal nerve root anatomy',
    directory: 'spine-anatomy'
  },
  {
    name: 'dermatome-map.jpg',
    url: 'https://images.unsplash.com/photo-1606672896853-d4f138e8e7b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Milad Fakurian on Unsplash',
    description: 'Dermatome mapping diagram',
    directory: 'spine-anatomy'
  },
  {
    name: 'facet-joint-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Facet joint anatomical structure',
    directory: 'spine-anatomy'
  },
  {
    name: 'facet-joint-grading.jpg',
    url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Facet joint degeneration grading',
    directory: 'spine-anatomy'
  },
  {
    name: 'disc-anatomy.jpg',
    url: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Jesse Orrico on Unsplash',
    description: 'Intervertebral disc anatomy',
    directory: 'spine-anatomy'
  },
  {
    name: 'degeneration-stages.jpg',
    url: 'https://images.unsplash.com/photo-1606672896853-d4f138e8e7b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Milad Fakurian on Unsplash',
    description: 'Disc degeneration progression',
    directory: 'spine-anatomy'
  },
  {
    name: 'degenerative-changes.jpg',
    url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Robina Weermeijer on Unsplash',
    description: 'Spinal degenerative changes',
    directory: 'spine-anatomy'
  },
  {
    name: 'spondylosis-progression.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by National Cancer Institute on Unsplash',
    description: 'Spondylosis progression stages',
    directory: 'spine-anatomy'
  },
  {
    name: 'spinal-curves.jpg',
    url: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    attribution: 'Photo by Jesse Orrico on Unsplash',
    description: 'Normal spinal curvature',
    directory: 'spine-anatomy'
  }
];

// All comprehensive images
const allComprehensiveImages = [
  ...brainConditionImages,
  ...neurologicalImages,
  ...peripheralNerveImages,
  ...spineAnatomyImages
];

function createDirectories() {
  console.log('Creating necessary directories...');
  const baseDir = path.join(__dirname, '..', 'public', 'images');
  
  const directories = [
    'brain-conditions',
    'neurological-conditions', 
    'peripheral-nerve-conditions',
    'spine-anatomy'
  ];
  
  directories.forEach(dir => {
    const fullPath = path.join(baseDir, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);
    } else {
      console.log(`📁 Directory exists: ${dir}`);
    }
  });
}

function downloadImage(imageInfo) {
  return new Promise((resolve, reject) => {
    const baseDir = path.join(__dirname, '..', 'public', 'images');
    const filePath = path.join(baseDir, imageInfo.directory, imageInfo.name);
    
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`⏭️  File already exists: ${imageInfo.name}`);
      resolve();
      return;
    }
    
    console.log(`📥 Downloading: ${imageInfo.name}`);
    console.log(`   URL: ${imageInfo.url}`);
    console.log(`   Description: ${imageInfo.description}`);
    
    const file = fs.createWriteStream(filePath);
    
    https.get(imageInfo.url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          console.log(`✅ Downloaded: ${imageInfo.name}`);
          
          // Create attribution file
          const attributionPath = path.join(baseDir, imageInfo.directory, `${imageInfo.name}-attribution.txt`);
          fs.writeFileSync(attributionPath, `${imageInfo.attribution}\nDescription: ${imageInfo.description}\nURL: ${imageInfo.url}`);
          
          resolve();
        });
      } else {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
      }
    }).on('error', (error) => {
      fs.unlink(filePath, () => {}); // Delete partial file
      reject(error);
    });
  });
}

async function downloadAllComprehensiveImages() {
  console.log('🚀 Starting comprehensive medical image download...');
  console.log(`📊 Total images to download: ${allComprehensiveImages.length}`);
  
  try {
    createDirectories();
    
    for (let i = 0; i < allComprehensiveImages.length; i++) {
      const imageInfo = allComprehensiveImages[i];
      console.log(`\n[${i + 1}/${allComprehensiveImages.length}] Processing: ${imageInfo.name}`);
      
      try {
        await downloadImage(imageInfo);
      } catch (error) {
        console.error(`❌ Failed to download ${imageInfo.name}:`, error.message);
      }
      
      // Respectful delay between downloads
      if (i < allComprehensiveImages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1500));
      }
    }
    
    console.log('\n🎉 Comprehensive download process completed!');
    console.log(`📁 Images organized in directories:`);
    console.log(`   - brain-conditions: ${brainConditionImages.length} images`);
    console.log(`   - neurological-conditions: ${neurologicalImages.length} images`);
    console.log(`   - peripheral-nerve-conditions: ${peripheralNerveImages.length} images`);
    console.log(`   - spine-anatomy: ${spineAnatomyImages.length} images`);
    
  } catch (error) {
    console.error('💥 Error during download process:', error);
  }
}

// Execute immediately
console.log('🔧 Comprehensive Medical Image Downloader Started');
downloadAllComprehensiveImages().catch(console.error);

export { downloadAllComprehensiveImages, allComprehensiveImages };