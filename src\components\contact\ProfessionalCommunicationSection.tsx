import { LucideIcon } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface CommunicationItem {
  label: string;
  value: string;
  description: string;
  hours?: string;
}

interface CommunicationSection {
  id: string;
  title: string;
  icon: LucideIcon;
  items: CommunicationItem[];
}

interface ProfessionalCommunicationSectionProps {
  title: string;
  subtitle: string;
  sections: CommunicationSection[];
}

const ProfessionalCommunicationSection: React.FC<ProfessionalCommunicationSectionProps> = ({
  title,
  subtitle,
  sections
}) => {
  const deviceInfo = useDeviceDetection();

  const getContactLink = (item: CommunicationItem) => {
    if (item.label.toLowerCase().includes('phone') || item.label.toLowerCase().includes('line')) {
      return `tel:${item.value.replace(/\s/g, '')}`;
    }
    if (item.label.toLowerCase().includes('email')) {
      return `mailto:${item.value}`;
    }
    if (item.label.toLowerCase().includes('fax')) {
      return `tel:${item.value.replace(/\s/g, '')}`;
    }
    return '#';
  };

  const isClickable = (item: CommunicationItem) => {
    return item.label.toLowerCase().includes('phone') || 
           item.label.toLowerCase().includes('email') || 
           item.label.toLowerCase().includes('line');
  };

  return (
    <section className="py-16 bg-muted">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-info">{title}</h2>
          <p className="text-xl text-info max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        <div className={cn(
          "grid gap-8",
          deviceInfo.isMobile 
            ? "grid-cols-1" 
            : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
        )}>
          {sections.map((section) => (
            <Card key={section.id} className="shadow-lg hover:shadow-xl transition-shadow bg-background">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-primary/10 rounded-lg">
                    <section.icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg text-primary">
                    {section.title}
                  </CardTitle>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {section.items.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-start">
                      <h4 className="font-semibold text-sm text-foreground">
                        {item.label}
                      </h4>
                      {item.hours && (
                        <Badge variant="outline" className="text-xs">
                          {item.hours}
                        </Badge>
                      )}
                    </div>
                    
                    {isClickable(item) ? (
                      <a 
                        href={getContactLink(item)}
                        className="block text-primary hover:text-primary/80 transition-colors font-medium"
                      >
                        {item.value}
                      </a>
                    ) : (
                      <p className="text-foreground font-medium">
                        {item.value}
                      </p>
                    )}
                    
                    <p className="text-foreground/90 text-xs leading-relaxed">
                      {item.description}
                    </p>
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Professional Resources */}
        <div className="mt-12">
          <Card className="max-w-4xl mx-auto shadow-lg bg-gradient-to-r from-primary/5 to-info-light border-primary/20">
            <CardContent className="p-8">
              <div className="text-center mb-6">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">
                  🩺 Professional Resources
                </h3>
                <p className="text-foreground/90 max-w-2xl mx-auto">
                  Dedicated communication channels for medical professionals, 
                  ensuring efficient collaboration and patient care coordination.
                </p>
              </div>
              
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile 
                  ? "grid-cols-1" 
                  : "grid-cols-1 md:grid-cols-2"
              )}>
                <div className="space-y-4">
                  <h4 className="font-bold text-primary">🔒 Secure Communication</h4>
                  <ul className="text-sm text-foreground/95 space-y-2">
                    <li>• HIPAA-compliant email encryption</li>
                    <li>• Secure fax transmission protocols</li>
                    <li>• HealthLink electronic referral system</li>
                    <li>• Argus medical imaging platform</li>
                  </ul>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-bold text-primary">⚡ Response Times</h4>
                  <ul className="text-sm text-foreground/95 space-y-2">
                    <li>• Urgent cases: Immediate response</li>
                    <li>• Professional inquiries: Within 4 hours</li>
                    <li>• Routine referrals: Within 24 hours</li>
                    <li>• Case discussions: Same day scheduling</li>
                  </ul>
                </div>
              </div>

              <div className="flex flex-wrap justify-center gap-4 mt-8">
                <Button asChild >
                  <a href="tel:**********" >
                    📞 Call Professional Line
                  </a>
                </Button>

                <Button asChild variant="outline" >
                  <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                    📧 Secure Email
                  </a>
                </Button>
                
                <Button asChild variant="outline" >
                  <a href="/gp-resources" className="text-primary hover:underline">
                    📋 GP Resources
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Communication Guidelines */}
        <div className="mt-8">
          <Card className="max-w-3xl mx-auto bg-info-light border-info/30">
            <CardContent className="p-6">
              <h3 className="font-bold text-info mb-4 text-center">
                📋 Professional Communication Guidelines
              </h3>
              <div className={cn(
                "grid gap-4 text-sm",
                deviceInfo.isMobile 
                  ? "grid-cols-1" 
                  : "grid-cols-2"
              )}>
                <div>
                  <h4 className="font-semibold text-info mb-2">For Urgent Referrals:</h4>
                  <ul className="text-info space-y-1">
                    <li>• Call (03) 9008 4200 directly</li>
                    <li>• Clearly state urgency level</li>
                    <li>• Provide patient contact details</li>
                    <li>• Include clinical summary</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold text-info mb-2">For Routine Referrals:</h4>
                  <ul className="text-info space-y-1">
                    <li>• Use HealthLink system (mineuros)</li>
                    <li>• Fax to (03) 9923 6688</li>
                    <li>• <NAME_EMAIL></li>
                    <li>• Include imaging via Argus</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ProfessionalCommunicationSection;
