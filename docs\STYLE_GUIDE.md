# Style Guide - The Golden Rule

## Overview

This document establishes the definitive style guide for the neurosurgeon website project. All contributors must adhere to these standards to ensure consistency across the entire codebase and user experience.

## Language Standards

### 1. User-Facing Content (British English)

**Rule**: All content that users see, read, or interact with MUST use British English spelling and terminology.

**Applies to**:
- Website text content
- User interface labels
- Button text
- Form labels and placeholders
- Error messages
- Success messages
- Tooltips and help text
- Navigation menu items
- Page titles and headings
- Meta descriptions
- Alt text for images
- Medical condition descriptions
- Patient information
- Location descriptions
- Service descriptions

**Examples**:
- ✅ "colour" (not "color")
- ✅ "centre" (not "center")
- ✅ "specialise" (not "specialize")
- ✅ "organisation" (not "organization")
- ✅ "realise" (not "realize")
- ✅ "behaviour" (not "behavior")
- ✅ "favour" (not "favor")
- ✅ "honour" (not "honor")
- ✅ "labour" (not "labor")
- ✅ "neighbour" (not "neighbor")

### 2. Code Elements (American English)

**Rule**: All code-related elements MUST use American English spelling conventions for consistency with international development standards.

**Applies to**:
- Variable names
- Function names
- Class names
- Interface names
- Type definitions
- Component names
- File names (when descriptive)
- Directory names
- CSS class names
- CSS custom properties
- JavaScript object properties
- API endpoint names
- Database field names
- Configuration keys
- Environment variable names
- Code comments
- Documentation for developers
- Git commit messages
- Technical documentation

**Examples**:
- ✅ `backgroundColor` (not `backgroundColour`)
- ✅ `getColorScheme()` (not `getColourScheme()`)
- ✅ `centerAlign` (not `centreAlign`)
- ✅ `organizationData` (not `organisationData`)
- ✅ `userBehavior` (not `userBehaviour`)
- ✅ `colorPrimary` (not `colourPrimary`)
- ✅ `text-center` (not `text-centre`)
- ✅ `--color-primary` (not `--colour-primary`)

## Implementation Guidelines

### 3. Mixed Context Handling

When code and user-facing content intersect:

**CSS Classes with User-Facing Impact**:
- Use American English in class names: `bg-color-primary`
- Use British English in user-visible content: `<span>Choose your favourite colour</span>`

**Data Structures**:
- Object keys: American English (`{ color: 'blue' }`)
- Display values: British English (`{ label: 'Favourite Colour' }`)

**API Responses**:
- Field names: American English (`{ backgroundColor: '#fff' }`)
- User messages: British English (`{ message: 'Colour updated successfully' }`)

### 4. File Organization

**Component Files**:
- File names: Use American English (`ColorPicker.tsx`, not `ColourPicker.tsx`)
- Component names: Use American English (`ColorPicker`, not `ColourPicker`)
- Props: Use American English (`backgroundColor`, not `backgroundColour`)
- User-facing text within components: Use British English

**Content Files**:
- Medical condition files: Use descriptive names in American English
- Content within files: Use British English for user-facing text

### 5. Documentation Standards

**Technical Documentation**:
- Use American English spelling
- Target audience: Developers
- Examples: API docs, component docs, setup guides

**User Documentation**:
- Use British English spelling
- Target audience: End users, patients, medical professionals
- Examples: User guides, help pages, medical information

## Enforcement

### 6. Quality Assurance

**Pre-commit Checks**:
- Automated spell checking for user-facing content
- Linting rules for code consistency
- Manual review for mixed contexts

**Review Process**:
- All pull requests must be reviewed for style guide compliance
- Reviewers should specifically check for language consistency
- Use this document as the definitive reference

**Tools and Automation**:
- ESLint rules for code naming conventions
- Spell checkers configured for British English on content files
- Custom scripts to detect common violations

### 7. Common Violations to Avoid

**❌ Wrong**:
```typescript
// American English in user-facing content
const message = "Select your favorite color";

// British English in code
const backgroundColour = "#ffffff";
function getCentrePosition() { ... }
```

**✅ Correct**:
```typescript
// British English in user-facing content
const message = "Select your favourite colour";

// American English in code
const backgroundColor = "#ffffff";
function getCenterPosition() { ... }
```

## Migration Strategy

### 8. Existing Code

When updating existing code:
1. **User-facing content**: Change to British English immediately
2. **Code elements**: Change to American English during refactoring
3. **Mixed contexts**: Handle case-by-case following guidelines above
4. **Document changes**: Update this guide if new patterns emerge

### 9. New Development

All new code must follow this style guide from the start:
- No exceptions for "quick fixes"
- Use this document during code reviews
- When in doubt, ask for clarification

## Conclusion

This style guide is the **Golden Rule** for our project. Consistency in language usage improves:
- User experience (familiar British English)
- Developer experience (standard American English conventions)
- Code maintainability
- Professional presentation

When in doubt, refer to this document. If a situation isn't covered, discuss with the team and update this guide accordingly.

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Status**: Active