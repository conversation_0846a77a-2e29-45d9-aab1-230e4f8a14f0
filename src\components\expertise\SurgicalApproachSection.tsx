import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface SurgicalApproachSectionProps {
  surgicalApproach: {
    title: string;
    subtitle: string;
    principles: {
      identification: {
        title: string;
        description: string;
      };
      access: {
        title: string;
        description: string;
      };
      repair: {
        title: string;
        description: string;
      };
    };
    cta: string;
  };
}

const SurgicalApproachSection: React.FC<SurgicalApproachSectionProps> = ({ surgicalApproach }) => {
  return (
    <section className="py-16 medical-card">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-6">{surgicalApproach.title}</h2>
          <p className="text-foreground/90">
            <a 
              href="https://mpscentre.com.au/dtTeam/dr-ales-<PERSON><PERSON><PERSON><PERSON>/" 
              className="text-primary hover:underline" 
              target="_blank" 
              rel="noopener noreferrer"
            >
              <PERSON>
            </a>
            {surgicalApproach.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="card p-6 rounded-lg shadow-md bg-background">
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-enhanced-heading text-2xl font-bold">
                1
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-center">
              {surgicalApproach.principles.identification.title}
            </h3>
            <p className="text-foreground/90 text-center">
              {surgicalApproach.principles.identification.description}
            </p>
          </div>

          <div className="card p-6 rounded-lg shadow-md bg-background">
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-enhanced-heading text-2xl font-bold">
                2
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-center">
              {surgicalApproach.principles.access.title}
            </h3>
            <p className="text-foreground/90 text-center">
              {surgicalApproach.principles.access.description}
            </p>
          </div>

          <div className="card p-6 rounded-lg shadow-md bg-background">
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-enhanced-heading text-2xl font-bold">
                3
              </div>
            </div>
            <h3 className="text-xl font-semibold mb-3 text-center">
              {surgicalApproach.principles.repair.title}
            </h3>
            <p className="text-foreground/90 text-center">
              {surgicalApproach.principles.repair.description}
            </p>
          </div>
        </div>

        <div className="mt-12 text-center">
          <Button asChild size="lg" >
            <Link to="/appointments" >{surgicalApproach.cta}</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

SurgicalApproachSection.displayName = 'SurgicalApproachSection';

export default SurgicalApproachSection;
