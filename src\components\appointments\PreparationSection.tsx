import { CheckCircle } from 'lucide-react';
import React from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface PreparationSection {
  title: string;
  items: string[];
}

interface PreparationSectionProps {
  title: string;
  subtitle: string;
  sections: PreparationSection[];
}

const PreparationSection: React.FC<PreparationSectionProps> = ({
  title,
  subtitle,
  sections
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-foreground/90 max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        <div className={cn(
          "grid gap-8",
          deviceInfo.isMobile 
            ? "grid-cols-1" 
            : "grid-cols-1 md:grid-cols-2"
        )}>
          {sections.map((section, index) => (
            <Card key={index} className="shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <CardTitle className="text-xl text-primary flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  {section.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent>
                <ul className="space-y-3">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                      <span className="text-foreground/90">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Tips */}
        <div className="mt-12">
          <Card className="bg-muted border-info">
            <CardHeader>
              <CardTitle className="text-xl text-info">
                💡 Helpful Tips
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
              )}>
                <div>
                  <h4 className="font-semibold text-foreground mb-2">Before Your Visit</h4>
                  <ul className="text-sm text-info space-y-1">
                    <li>• Prepare a list of questions you want to ask</li>
                    <li>• Write down your symptoms and when they started</li>
                    <li>• Note any treatments you've already tried</li>
                    <li>• Consider bringing a support person</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-foreground mb-2">Day of Appointment</h4>
                  <ul className="text-sm text-info space-y-1">
                    <li>• Arrive 15 minutes early for check-in</li>
                    <li>• Wear comfortable, loose-fitting clothing</li>
                    <li>• Bring reading material for waiting time</li>
                    <li>• Turn off mobile phone during consultation</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Important Note */}
        <div className="mt-8 text-center">
          <Card className="max-w-2xl mx-auto bg-info-light border-info/30">
            <CardContent className="p-6">
              <h3 className="font-bold text-foreground mb-2">
                ⚠️ Important Note
              </h3>
              <p className="text-foreground text-sm">
                If you were injured in a workplace, traffic, or other accident, please provide an accurate 
                description of the incident circumstances, as they play a major role in understanding the 
                mechanism of injury and planning appropriate management strategy.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default PreparationSection;
