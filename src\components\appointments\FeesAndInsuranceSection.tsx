import { LucideIcon } from 'lucide-react';
import React from 'react';

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Consultation {
  title: string;
  description: string;
  fee: string;
  rebate: string;
  outOfPocket: string;
}

interface InsuranceOption {
  id: string;
  title: string;
  icon: LucideIcon;
  description: string;
  details: string[];
}

interface FeesAndInsuranceSectionProps {
  feesData: {
    title: string;
    subtitle: string;
    note: string;
    consultations: Consultation[];
  };
  insuranceData: {
    title: string;
    subtitle: string;
    options: InsuranceOption[];
  };
}

const FeesAndInsuranceSection: React.FC<FeesAndInsuranceSectionProps> = ({
  feesData,
  insuranceData
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className="py-16">
      <div className="container">
        <Tabs defaultValue="fees" className="w-full">
          <TabsList className={cn(
            "grid w-full grid-cols-2 mb-8",
            deviceInfo.isMobile ? "h-auto" : ""
          )}>
            <TabsTrigger value="fees" className="text-center py-3">
              Consultation Fees
            </TabsTrigger>
            <TabsTrigger value="insurance" className="text-center py-3">
              Insurance Options
            </TabsTrigger>
          </TabsList>

          <TabsContent value="fees" className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{feesData.title}</h2>
              <p className="text-foreground/90 max-w-3xl mx-auto mb-4">
                {feesData.subtitle}
              </p>
              <p className="text-sm text-foreground/95 italic">
                {feesData.note}
              </p>
            </div>

            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
            )}>
              {feesData.consultations.map((consultation, index) => (
                <Card key={index} className="shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-xl text-primary">
                      {consultation.title}
                    </CardTitle>
                    <p className="text-foreground/90 text-sm">
                      {consultation.description}
                    </p>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center py-2 border-b">
                        <span className="font-medium">Consultation Fee</span>
                        <span className="text-lg font-bold text-primary">
                          {consultation.fee}
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center py-2 border-b">
                        <span className="font-medium">Medicare Rebate</span>
                        <span className="text-success font-semibold">
                          {consultation.rebate}
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center py-2">
                        <span className="font-medium">Out-of-Pocket Cost</span>
                        <span className="text-lg font-bold">
                          {consultation.outOfPocket}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="insurance" className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{insuranceData.title}</h2>
              <p className="text-foreground/90 max-w-3xl mx-auto">
                {insuranceData.subtitle}
              </p>
            </div>

            <div className="grid gap-6">
              {insuranceData.options.map((option, _index) => (
                <Card key={option.id} className="shadow-md">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <option.icon className="h-6 w-6 text-primary" />
                      <CardTitle className="text-xl">{option.title}</CardTitle>
                    </div>
                    <p className="text-foreground/90">
                      {option.description}
                    </p>
                  </CardHeader>
                  
                  <CardContent>
                    <ul className="list-disc list-inside text-sm text-foreground/95 space-y-2">
                      {option.details.map((detail, detailIndex) => (
                        <li key={detailIndex}>{detail}</li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
};

export default FeesAndInsuranceSection;
