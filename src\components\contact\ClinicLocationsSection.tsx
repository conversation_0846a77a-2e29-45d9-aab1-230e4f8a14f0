import { MapPin, Phone, Mail, Clock, Car, Train, Accessibility } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Address {
  street: string;
  suburb: string;
  state: string;
  postcode: string;
  country: string;
}

interface Contact {
  phone: string;
  fax: string;
  email: string;
}

interface Hours {
  weekdays: string;
  weekends: string;
  holidays: string;
}

interface Accessibility {
  wheelchairAccess: boolean;
  disabledParking: boolean;
  elevator: boolean;
  publicTransport: string;
}

interface Facilities {
  parking: string;
  waitingArea: string;
  consultationRooms: string;
  diagnosticEquipment: string;
}

interface Coordinates {
  lat: number;
  lng: number;
}

interface ClinicLocation {
  id: string;
  name: string;
  isPrimary: boolean;
  address: Address;
  contact: Contact;
  hours: Hours;
  accessibility: Accessibility;
  facilities: Facilities;
  coordinates: Coordinates;
}

interface ClinicLocationsSectionProps {
  title: string;
  subtitle: string;
  locations: ClinicLocation[];
}

const ClinicLocationsSection: React.FC<ClinicLocationsSectionProps> = ({
  title,
  subtitle,
  locations
}) => {
  const deviceInfo = useDeviceDetection();

  const getGoogleMapsUrl = (location: ClinicLocation) => {
    const address = `${location.address.street}, ${location.address.suburb} ${location.address.state} ${location.address.postcode}`;
    return `https://maps.google.com/?q=${encodeURIComponent(address)}`;
  };

  const getDirectionsUrl = (location: ClinicLocation) => {
    return `https://maps.google.com/maps/dir/?api=1&destination=${location.coordinates.lat},${location.coordinates.lng}`;
  };

  return (
    <section className="py-16 bg-muted/30">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-foreground/90 max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        <div className={cn(
          "grid gap-8",
          deviceInfo.isMobile 
            ? "grid-cols-1" 
            : "grid-cols-1 lg:grid-cols-2"
        )}>
          {locations.map((location) => (
            <Card 
              key={location.id} 
              className={cn(
                "shadow-lg hover:shadow-xl transition-shadow",
                location.isPrimary && "ring-2 ring-primary/20"
              )}
            >
              <CardHeader>
                <div className="flex justify-between items-start mb-2">
                  <CardTitle className="text-xl text-primary">
                    {location.name}
                  </CardTitle>
                  {location.isPrimary && (
                    <Badge className="bg-primary">Primary Office</Badge>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Address */}
                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold mb-1">Address</h4>
                    <p className="text-foreground/90 text-sm">
                      {location.address.street}<br />
                      {location.address.suburb} {location.address.state} {location.address.postcode}<br />
                      {location.address.country}
                    </p>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-1">Phone</h4>
                      <a 
                        href={`tel:${location.contact.phone.replace(/\s/g, '')}`}
                        className="text-foreground/90 text-sm hover:text-primary transition-colors"
                      >
                        {location.contact.phone}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-1">Email</h4>
                      <a 
                        href={`mailto:${location.contact.email}`}
                        className="text-foreground/90 text-sm hover:text-primary transition-colors"
                      >
                        {location.contact.email}
                      </a>
                    </div>
                  </div>
                </div>

                {/* Hours */}
                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold mb-1">Hours</h4>
                    <p className="text-foreground/90 text-sm">
                      {location.hours.weekdays}<br />
                      {location.hours.weekends}
                    </p>
                  </div>
                </div>

                {/* Accessibility Features */}
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Accessibility className="h-5 w-5 text-primary" />
                    Accessibility
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {location.accessibility.wheelchairAccess && (
                      <Badge variant="secondary">Wheelchair Access</Badge>
                    )}
                    {location.accessibility.disabledParking && (
                      <Badge variant="secondary">Disabled Parking</Badge>
                    )}
                    {location.accessibility.elevator && (
                      <Badge variant="secondary">Elevator Access</Badge>
                    )}
                  </div>
                  <p className="text-foreground/90 text-sm mt-2">
                    <Train className="h-4 w-4 inline mr-1" />
                    {location.accessibility.publicTransport}
                  </p>
                </div>

                {/* Facilities */}
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Car className="h-5 w-5 text-primary" />
                    Facilities
                  </h4>
                  <ul className="text-foreground/90 text-sm space-y-1">
                    <li>• {location.facilities.parking}</li>
                    <li>• {location.facilities.waitingArea}</li>
                    <li>• {location.facilities.consultationRooms}</li>
                    <li>• {location.facilities.diagnosticEquipment}</li>
                  </ul>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 pt-4">
                  <Button asChild variant="outline" size="sm" >
                    <a
                      href={getGoogleMapsUrl(location)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      <MapPin className="h-4 w-4 mr-2" />
                      View on Map
                    </a>
                  </Button>

                  <Button asChild variant="outline" size="sm" >
                    <a
                      href={getDirectionsUrl(location)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      Get Directions
                    </a>
                  </Button>

                  <Button asChild size="sm" >
                    <a href={`tel:${location.contact.phone.replace(/\s/g, '')}`} >
                      <Phone className="h-4 w-4 mr-2" />
                      Call Now
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Information */}
        <div className="mt-12 text-center">
          <Card className="max-w-3xl mx-auto bg-muted border-info">
            <CardContent className="p-8">
              <h3 className="text-xl font-bold mb-4 text-info">
                🏥 Multiple Convenient Locations
              </h3>
              <p className="text-info mb-6">
                Dr. Aliashkevich consults at 11 locations across Melbourne for your convenience. 
                All locations offer modern facilities, wheelchair accessibility, and convenient parking.
              </p>
              <Button asChild >
                <a href="/locations" >
                  View All Locations
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ClinicLocationsSection;
