import React from 'react';

import { Button } from '@/components/ui/button';

interface ConsultingRoomsHeroProps {
  subtitle: string;
  title: string;
  description: string;
  ctaText: string;
  ctaLink: string;
}

const ConsultingRoomsHero: React.FC<ConsultingRoomsHeroProps> = ({
  subtitle,
  title,
  description,
  ctaText,
  ctaLink
}) => {
  return (
    <section className="relative py-20 bg-gradient-to-r from-primary/10 to-background dark:from-primary/20 dark:to-background">
      <div className="container relative z-10">
        <div className="text-center max-w-3xl mx-auto">
          <p className="text-sm uppercase tracking-wider text-primary font-medium mb-2">
            {subtitle}
          </p>
          <h1 className="text-enhanced-heading text-4xl md:text-5xl font-bold mt-2 mb-6">
            {title}
          </h1>
          <p className="text-enhanced-body">
            {description}
          </p>
          <div className="mt-8">
            <Button asChild size="lg" >
              <a href={ctaLink} >{ctaText}</a>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

ConsultingRoomsHero.displayName = 'ConsultingRoomsHero';

export default ConsultingRoomsHero;
