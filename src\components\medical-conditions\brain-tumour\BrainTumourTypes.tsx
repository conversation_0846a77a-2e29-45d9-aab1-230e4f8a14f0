import { Brain, Target, Info, ChevronDown, ChevronUp, TrendingUp } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TumourType {
  name: string;
  description: string;
  characteristics: string[];
  prognosis: string;
  treatmentApproach: string;
}

interface SecondaryTumours {
  description: string;
  commonSources: string[];
  characteristics: string[];
}

interface BrainTumourTypesProps {
  title: string;
  description: string;
  primaryTumours: TumourType[];
  secondaryTumours: SecondaryTumours;
}

export function BrainTumourTypes({ 
  title, 
  description, 
  primaryTumours, 
  secondaryTumours 
}: BrainTumourTypesProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedTumour, setExpandedTumour] = useState<string | null>(null);

  const toggleExpanded = (tumourName: string) => {
    setExpandedTumour(expandedTumour === tumourName ? null : tumourName);
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-foreground/90 max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        <Tabs defaultValue="primary" className="w-full">
          <TabsList className={cn(
            "grid w-full mb-8",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : "grid-cols-2"
          )}>
            <TabsTrigger 
              value="primary"
              className={cn(
                "flex items-center gap-2",
                deviceInfo.isMobile ? "py-3" : "py-4"
              )}
            >
              <Brain className="h-4 w-4" />
              Primary Brain Tumours
            </TabsTrigger>
            <TabsTrigger 
              value="secondary"
              className={cn(
                "flex items-center gap-2",
                deviceInfo.isMobile ? "py-3" : "py-4"
              )}
            >
              <Target className="h-4 w-4" />
              Secondary (Metastatic) Tumours
            </TabsTrigger>
          </TabsList>

          <TabsContent value="primary" className="space-y-6">
            <div className="grid gap-6">
              {primaryTumours.map((tumour, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Brain className="h-5 w-5 text-primary" />
                        {tumour.name}
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(tumour.name)}
                        className="flex items-center gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {expandedTumour === tumour.name ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <CardDescription>{tumour.description}</CardDescription>
                  </CardHeader>
                  
                  <CardContent>
                    {/* Key Characteristics */}
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2">Key Characteristics:</h4>
                      <ul className="space-y-1">
                        {tumour.characteristics.slice(0, 2).map((characteristic, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="text-sm">{characteristic}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <Collapsible open={expandedTumour === tumour.name}>
                      <CollapsibleContent className="space-y-4">
                        {/* Additional Characteristics */}
                        {tumour.characteristics.length > 2 && (
                          <div>
                            <h4 className="font-semibold mb-2">Additional Details:</h4>
                            <ul className="space-y-1">
                              {tumour.characteristics.slice(2).map((characteristic, idx) => (
                                <li key={idx} className="flex items-start gap-2">
                                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                                  <span className="text-sm">{characteristic}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {/* Prognosis and Treatment */}
                        <div className={cn(
                          "grid gap-4",
                          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                        )}>
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center gap-2">
                              <TrendingUp className="h-4 w-4 text-success" />
                              Prognosis
                            </h4>
                            <p className="text-sm text-foreground/95">{tumour.prognosis}</p>
                          </div>
                          <div>
                            <h4 className="font-semibold mb-2 flex items-center gap-2">
                              <Target className="h-4 w-4 text-info" />
                              Treatment Approach
                            </h4>
                            <p className="text-sm text-foreground/95">{tumour.treatmentApproach}</p>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="secondary" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-primary" />
                  Secondary (Metastatic) Brain Tumours
                </CardTitle>
                <CardDescription>{secondaryTumours.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Common Sources */}
                <div>
                  <h4 className="font-semibold mb-3">Most Common Sources:</h4>
                  <div className={cn(
                    "grid gap-2",
                    deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 md:grid-cols-3"
                  )}>
                    {secondaryTumours.commonSources.map((source, index) => (
                      <Badge key={index} variant="secondary" className="justify-center py-2">
                        {source}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Characteristics */}
                <div>
                  <h4 className="font-semibold mb-3">Characteristics:</h4>
                  <ul className="space-y-2">
                    {secondaryTumours.characteristics.map((characteristic, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm">{characteristic}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Important Note */}
                <div className="bg-info-light border border-info/30 rounded-lg p-4">
                  <div className="flex items-start gap-2">
                    <Info className="h-5 w-5 text-info mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-info mb-1">Important Note</h4>
                      <p className="text-sm text-foreground">
                        Secondary brain tumours require treatment of both the brain metastases and the primary cancer. 
                        A multidisciplinary team approach involving neurosurgery, oncology, and radiation oncology 
                        is essential for optimal outcomes.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2">Need a Precise Diagnosis?</h3>
              <p className="text-foreground/90 mb-4">
                Accurate tumour classification is crucial for determining the best treatment approach. 
                Our team uses advanced imaging and diagnostic techniques for precise diagnosis.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"} >
                Schedule Diagnostic Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default BrainTumourTypes;
