import React from 'react';

interface LocationAmenitiesProps {
  title: string;
  description: string;
  locationDetails: string;
  medicalFacilities: {
    title: string;
    description: string;
    additionalInfo: string;
  };
  surroundingAmenities: {
    title: string;
    description: string;
    additionalInfo: string;
  };
  transportation: {
    title: string;
    items: string[];
  };
  parking: {
    title: string;
    description: string;
    additionalInfo: string;
  };
  images: Array<{
    src: string;
    alt: string;
  }>;
}

/**
 * Location Amenities Component
 * Displays location details, nearby facilities, and transportation options
 * Preserves all original amenities content from Surrey Hills location page
 */
const LocationAmenities: React.FC<LocationAmenitiesProps> = ({
  title,
  description,
  locationDetails,
  medicalFacilities,
  surroundingAmenities,
  transportation,
  parking,
  images
}) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-enhanced-body">
            {description}
          </p>
        </div>

        <div className="mt-8 max-w-3xl mx-auto mb-12">
          <p className="text-enhanced-body text-center">
            {locationDetails}
          </p>
        </div>

        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">{medicalFacilities.title}</h3>
            <p className="text-foreground/90 mb-4">
              {medicalFacilities.description}
            </p>
            <p className="text-foreground/90">
              {medicalFacilities.additionalInfo}
            </p>
          </div>

          {images[0] && (
            <div className="relative h-64 rounded-lg overflow-hidden shadow-xl">
              <img
                src={images[0].src}
                alt={images[0].alt}
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
          )}

          {images[1] && (
            <div className="relative h-64 rounded-lg overflow-hidden shadow-xl">
              <img
                src={images[1].src}
                alt={images[1].alt}
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
          )}

          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">{surroundingAmenities.title}</h3>
            <p className="text-foreground/90 mb-4">
              {surroundingAmenities.description}
            </p>
            <p className="text-foreground/90">
              {surroundingAmenities.additionalInfo}
            </p>
          </div>

          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">{transportation.title}</h3>
            <ul className="text-foreground/90 list-none space-y-2">
              {transportation.items.map((item, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">{parking.title}</h3>
            <p className="text-foreground/90 mb-4">
              {parking.description}
            </p>
            <p className="text-foreground/90">
              {parking.additionalInfo}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

LocationAmenities.displayName = 'LocationAmenities';

export default LocationAmenities;
