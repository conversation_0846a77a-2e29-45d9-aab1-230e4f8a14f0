import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface IntroductionSectionProps {
  introduction: {
    paragraph1: string;
    paragraph2: string;
    paragraph3: string;
  };
}

const IntroductionSection: React.FC<IntroductionSectionProps> = ({ introduction }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-background",
      deviceInfo.isMobile ? "py-12" : "py-20"
    )}>
      <div className={cn(
        "container max-w-5xl mx-auto",
        deviceInfo.isMobile ? "px-4" : "px-6"
      )}>
        <div className="text-center space-y-8">
          {/* First paragraph with doctor name */}
          <div className={cn(
            "text-foreground leading-relaxed",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            <p className="mb-6">
              <a
                href="https://mpscentre.com.au/dtTeam/dr-ales-<PERSON><PERSON><PERSON><PERSON>/"
                className={cn(
                  "text-primary font-semibold transition-colors duration-200",
                  deviceInfo.isMobile ? "touch-manipulation" : "hover:text-primary/80 hover:underline"
                )}
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Learn more about Dr Ales Aliashkevich (opens in new tab)"
              >
                Dr Ales Aliashkevich
              </a>{" "}
              <span className="text-foreground/90">
                {introduction.paragraph1}
              </span>
            </p>
          </div>

          {/* Second paragraph with doctor reference */}
          <div className={cn(
            "text-foreground leading-relaxed",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            <p className="mb-6">
              <a
                href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/"
                className={cn(
                  "text-primary font-semibold transition-colors duration-200",
                  deviceInfo.isMobile ? "touch-manipulation" : "hover:text-primary/80 hover:underline"
                )}
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Learn more about Dr Aliashkevich (opens in new tab)"
              >
                Dr Aliashkevich
              </a>{" "}
              <span className="text-foreground/90">
                {introduction.paragraph2}
              </span>
            </p>
          </div>

          {/* Third paragraph */}
          <div className={cn(
            "text-foreground/90 leading-relaxed",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            <p className="max-w-4xl mx-auto">
              {introduction.paragraph3}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

IntroductionSection.displayName = 'IntroductionSection';

export default IntroductionSection;
