#!/usr/bin/env node

/**
 * COMPREHENSIVE MUTED FOREGROUND REPLACEMENT
 * 
 * This script replaces ALL instances of text-muted-foreground
 * with better contrast alternatives for improved readability.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.join(__dirname, '..');

console.log('🔧 REPLACING ALL text-muted-foreground WITH BETTER CONTRAST...\n');

// COMPREHENSIVE MUTED FOREGROUND REPLACEMENTS
const MUTED_FOREGROUND_FIXES = [
  // 1. Basic text-muted-foreground replacements
  {
    pattern: /\btext-muted-foreground\b/g,
    replacement: 'text-foreground/90',
    desc: 'IMPROVED: text-muted-foreground → text-foreground/90 (better contrast)'
  },
  
  // 2. Small text with muted foreground (needs even better contrast)
  {
    pattern: /className="([^"]*?)text-xs([^"]*?)text-foreground\/90([^"]*?)"/g,
    replacement: 'className="$1text-xs$2text-foreground/95$3"',
    desc: 'ENHANCED: Small text with better contrast'
  },
  {
    pattern: /className="([^"]*?)text-sm([^"]*?)text-foreground\/90([^"]*?)"/g,
    replacement: 'className="$1text-sm$2text-foreground/95$3"',
    desc: 'ENHANCED: Small text with better contrast'
  },
  
  // 3. Dark theme muted foreground
  {
    pattern: /\bdark:text-muted-foreground\b/g,
    replacement: 'dark:text-foreground/90',
    desc: 'IMPROVED: dark:text-muted-foreground → dark:text-foreground/90'
  },
  
  // 4. Combined classes with muted foreground
  {
    pattern: /className="([^"]*?)text-muted-foreground([^"]*?)text-center([^"]*?)"/g,
    replacement: 'className="$1text-foreground/90$2text-center$3"',
    desc: 'IMPROVED: Centered muted text → better contrast'
  },
  {
    pattern: /className="([^"]*?)text-muted-foreground([^"]*?)mb-([0-9]+)([^"]*?)"/g,
    replacement: 'className="$1text-foreground/90$2mb-$3$4"',
    desc: 'IMPROVED: Muted text with margin → better contrast'
  },
  
  // 5. Specific component patterns
  {
    pattern: /className="([^"]*?)text-muted-foreground([^"]*?)font-medium([^"]*?)"/g,
    replacement: 'className="$1text-foreground/95$2font-medium$3"',
    desc: 'ENHANCED: Medium weight muted text → better contrast'
  },
  {
    pattern: /className="([^"]*?)text-muted-foreground([^"]*?)leading-relaxed([^"]*?)"/g,
    replacement: 'className="$1text-foreground/90$2leading-relaxed$3"',
    desc: 'IMPROVED: Relaxed leading muted text → better contrast'
  },
  
  // 6. List and description text
  {
    pattern: /className="([^"]*?)list-disc([^"]*?)text-foreground\/90([^"]*?)"/g,
    replacement: 'className="$1list-disc$2text-foreground/95$3"',
    desc: 'ENHANCED: List items with better contrast'
  },
  
  // 7. Form and input related muted text
  {
    pattern: /className="([^"]*?)text-foreground\/90([^"]*?)placeholder([^"]*?)"/g,
    replacement: 'className="$1text-foreground/95$2placeholder$3"',
    desc: 'ENHANCED: Form text with better contrast'
  }
];

// Files to process
const DIRECTORIES_TO_PROCESS = [
  path.join(PROJECT_ROOT, 'src'),
];

const FILE_EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];

// Get all files to process
function getAllFiles(dir, extensions) {
  let results = [];
  
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat && stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build', '.next'].includes(file)) {
          results = results.concat(getAllFiles(filePath, extensions));
        }
      } else {
        if (extensions.some(ext => file.endsWith(ext))) {
          results.push(filePath);
        }
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return results;
}

// Process a single file
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    let fixesApplied = [];
    
    // Apply all fixes
    MUTED_FOREGROUND_FIXES.forEach(fix => {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
        fixesApplied.push(`   ✅ ${fix.desc} (${matches.length} fixes)`);
      }
    });
    
    // Write back if modified
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      const relativePath = path.relative(PROJECT_ROOT, filePath);
      console.log(`🔧 MUTED FOREGROUND: ${fixesApplied.length} issues fixed in ${relativePath}`);
      fixesApplied.forEach(fix => console.log(fix));
      console.log('');
      return fixesApplied.length;
    }
    
    return 0;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
    return 0;
  }
}

// Main function
async function main() {
  try {
    let totalFiles = 0;
    let totalFixes = 0;
    
    // Process all directories
    for (const dir of DIRECTORIES_TO_PROCESS) {
      const files = getAllFiles(dir, FILE_EXTENSIONS);
      
      for (const file of files) {
        const fixes = processFile(file);
        if (fixes > 0) {
          totalFiles++;
          totalFixes += fixes;
        }
      }
    }
    
    console.log('🎯 MUTED FOREGROUND REPLACEMENT COMPLETE!');
    console.log(`📊 Total files updated: ${totalFiles}`);
    console.log(`📊 Total muted foreground issues fixed: ${totalFixes}`);
    console.log('');
    
    if (totalFixes === 0) {
      console.log('✅ NO muted foreground issues found!');
      console.log('🎉 All text already has optimal contrast.');
    } else {
      console.log('✅ ALL muted foreground text improved!');
      console.log('🔍 Text contrast significantly enhanced for better readability.');
    }
    
  } catch (error) {
    console.error('❌ Error during muted foreground replacement:', error);
    process.exit(1);
  }
}

// Run the script
main();