import { <PERSON>ert<PERSON><PERSON><PERSON>, <PERSON>, Clock, Zap, Brain, Eye, Activity } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface EmergencySign {
  sign: string;
  description: string;
  action: string;
  urgency: 'immediate' | 'urgent' | 'prompt';
}

interface WarningSignsSectionProps {
  title: string;
  description: string;
  emergencySigns: EmergencySign[];
}

export function WarningSignsSection({ 
  title, 
  description, 
  emergencySigns 
}: WarningSignsSectionProps) {
  const deviceInfo = useDeviceDetection();
  const [selectedUrgency, setSelectedUrgency] = useState<string>('all');

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'badge-emergency';
      case 'urgent': return 'badge-info';
      case 'prompt': return 'badge-info';
      default: return 'bg-muted text-foreground/90 border-border';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return <AlertTriangle className="h-5 w-5 text-foreground" />;
      case 'urgent': return <Clock className="h-5 w-5 text-info dark:text-info" />;
      case 'prompt': return <Eye className="h-5 w-5 text-info dark:text-info" />;
      default: return <Activity className="h-5 w-5 text-foreground/90" />;
    }
  };

  const getSignIcon = (sign: string) => {
    if (sign.toLowerCase().includes('headache')) return Zap;
    if (sign.toLowerCase().includes('vision') || sign.toLowerCase().includes('visual')) return Eye;
    if (sign.toLowerCase().includes('consciousness') || sign.toLowerCase().includes('neurological')) return Brain;
    return AlertTriangle;
  };

  const filteredSigns = selectedUrgency === 'all' 
    ? emergencySigns 
    : emergencySigns.filter(sign => sign.urgency === selectedUrgency);

  return (
    <section className={cn("section-spacing section-background", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4 text-foreground",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Emergency Contact Information */}
        <div className="mb-12">
          <Card className="bg-muted/50 text-foreground border-border">
            <CardContent className="pt-6">
              <div className="text-center">
                <Phone className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-enhanced-heading text-2xl font-bold mb-2 text-foreground">Emergency Contact</h3>
                <p className="text-xl mb-4 text-foreground">Call 000 immediately for any sudden severe symptoms</p>
                <div className={cn(
                  "grid gap-4",
                  deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                )}>
                  <div className="bg-muted/50 rounded-lg p-4">
                    <h4 className="font-semibold mb-1">Emergency Services</h4>
                    <p className="text-lg font-bold">000</p>
                  </div>
                  <div className="bg-muted/50 rounded-lg p-4">
                    <h4 className="font-semibold mb-1">Poison Information</h4>
                    <p className="text-lg font-bold">13 11 26</p>
                  </div>
                  <div className="bg-muted/50 rounded-lg p-4">
                    <h4 className="font-semibold mb-1">Health Direct</h4>
                    <p className="text-lg font-bold">1800 022 222</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Urgency Filter */}
        <div className="mb-8">
          <div className="flex justify-center">
            <div className={cn(
              "flex bg-card rounded-lg p-1 shadow-sm border border-border",
              deviceInfo.isMobile ? "flex-col w-full" : "flex-row"
            )}>
              <Button
                variant={selectedUrgency === 'all' ? "default" : "ghost"}
                size="sm"
                onClick={() => setSelectedUrgency('all')}
                className={cn(
                  deviceInfo.isMobile ? "justify-start" : "",
                  selectedUrgency === 'all'
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "text-foreground/90 hover:text-foreground hover:bg-muted"
                )}
              >
                All Warning Signs
              </Button>
              <Button
                variant={selectedUrgency === 'immediate' ? "muted" : "ghost"}
                size="sm"
                onClick={() => setSelectedUrgency('immediate')}
                className={cn(
                  deviceInfo.isMobile ? "justify-start" : "",
                  selectedUrgency === 'immediate'
                    ? "bg-error text-foreground-foreground hover:bg-error/90"
                    : "text-foreground/90 hover:text-foreground hover:bg-muted"
                )}
              >
                <AlertTriangle className="mr-2 h-4 w-4" />
                Immediate
              </Button>
              <Button
                variant={selectedUrgency === 'urgent' ? "default" : "ghost"}
                size="sm"
                onClick={() => setSelectedUrgency('urgent')}
                className={cn(
                  deviceInfo.isMobile ? "justify-start" : "",
                  selectedUrgency === 'urgent'
                    ? "bg-status-warning text-status-warning-foreground hover:bg-status-warning/90"
                    : "text-foreground/90 hover:text-foreground hover:bg-muted"
                )}
              >
                <Clock className="mr-2 h-4 w-4" />
                Urgent
              </Button>
              <Button
                variant={selectedUrgency === 'prompt' ? "default" : "ghost"}
                size="sm"
                onClick={() => setSelectedUrgency('prompt')}
                className={cn(
                  deviceInfo.isMobile ? "justify-start" : "",
                  selectedUrgency === 'prompt'
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "text-foreground/90 hover:text-foreground hover:bg-muted"
                )}
              >
                <Eye className="mr-2 h-4 w-4" />
                Prompt
              </Button>
            </div>
          </div>
        </div>

        {/* Warning Signs Grid */}
        <div className={cn(
          "grid gap-6 mb-12",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
        )}>
          {filteredSigns.map((emergencySign, index) => {
            const SignIcon = getSignIcon(emergencySign.sign);
            
            return (
              <Card 
                key={index} 
                className={cn(
                  "border-2 transition-all duration-200 hover:shadow-lg",
                  getUrgencyColor(emergencySign.urgency)
                )}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <SignIcon className="h-5 w-5" />
                      </div>
                      <CardTitle className="text-lg">{emergencySign.sign}</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      {getUrgencyIcon(emergencySign.urgency)}
                      <Badge className={getUrgencyColor(emergencySign.urgency)}>
                        {emergencySign.urgency.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm mb-4">{emergencySign.description}</p>
                  <div className="bg-background/50 border rounded-lg p-3">
                    <h4 className="font-semibold mb-1 flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Action Required:
                    </h4>
                    <p className="text-sm font-medium">{emergencySign.action}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Urgency Levels Explanation */}
        <div className="mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                Understanding Urgency Levels
              </CardTitle>
              <CardDescription>
                Different symptoms require different response times
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="bg-muted/30 border border-border/70 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-foreground" />
                    <h4 className="font-semibold text-foreground">Immediate</h4>
                  </div>
                  <p className="text-sm text-foreground/80">
                    Call 000 immediately. These symptoms suggest possible aneurysm rupture
                    and require emergency medical intervention within minutes.
                  </p>
                </div>
                <div className="bg-info dark:bg-info/20 border border-info dark:border-info rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-5 w-5 text-info dark:text-info" />
                    <h4 className="font-semibold text-info dark:text-info">Urgent</h4>
                  </div>
                  <p className="text-sm text-info dark:text-info">
                    Seek medical attention within hours. These symptoms may indicate
                    aneurysm enlargement or other serious complications.
                  </p>
                </div>
                <div className="bg-info border border-info rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Eye className="h-5 w-5 text-info" />
                    <h4 className="font-semibold text-info">Prompt</h4>
                  </div>
                  <p className="text-sm text-info">
                    Schedule medical evaluation within days. These symptoms warrant 
                    professional assessment but are not immediately life-threatening.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* What NOT to Do */}
        <div className="mb-12">
          <Card className="bg-muted border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground/90">
                <AlertTriangle className="h-5 w-5" />
                What NOT to Do in an Emergency
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h4 className="font-semibold mb-2 text-foreground">Do NOT:</h4>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Wait to see if symptoms improve</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Drive yourself to the hospital</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Take pain medication and wait</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Assume it's just a migraine</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 text-success">DO:</h4>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Call 000 immediately for severe symptoms</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Stay calm and follow emergency instructions</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Have someone stay with you</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Bring medication list to hospital</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Card className="bg-muted/30 dark:bg-muted/20 border-border/20">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2 text-foreground">Emergency Preparedness</h3>
              <p className="text-foreground/80 mb-4">
                If you have a known cerebral aneurysm, ensure your family and friends know these warning signs
                and have emergency contact information readily available.
              </p>
              <div className={cn(
                "flex gap-3",
                deviceInfo.isMobile ? "flex-col" : "flex-row justify-center"
              )}>
                <Button size="lg" variant="muted" >
                  <Phone className="mr-2 h-4 w-4" />
                  Emergency Contacts
                </Button>
                <Button variant="outline" size="lg" >
                  Download Emergency Card
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default WarningSignsSection;
