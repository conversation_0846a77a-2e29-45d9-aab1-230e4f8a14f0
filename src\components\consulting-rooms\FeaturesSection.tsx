import { MapPin, Building, Clock } from 'lucide-react';
import React from 'react';

import { FeatureCard } from '@/data/consultingRooms/consultingRoomsData';

interface FeaturesSectionProps {
  title: string;
  description: string;
  features: FeatureCard[];
}

const iconMap = {
  MapPin,
  Building,
  Clock
};

const FeaturesSection: React.FC<FeaturesSectionProps> = ({
  title,
  description,
  features
}) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-enhanced-body">{description}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {features.map((feature) => {
            const IconComponent = iconMap[feature.icon as keyof typeof iconMap];
            
            return (
              <div key={feature.id} className="card p-6 rounded-lg shadow-md medical-card">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  {IconComponent && (
                    <IconComponent className="h-6 w-6 text-primary" />
                  )}
                </div>
                <h3 className="text-xl font-semibold mb-3 text-primary">{feature.title}</h3>
                <p className="text-foreground/90">{feature.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

FeaturesSection.displayName = 'FeaturesSection';

export default FeaturesSection;
