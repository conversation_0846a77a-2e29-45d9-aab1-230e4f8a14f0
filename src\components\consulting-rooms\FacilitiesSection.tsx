
// TODO: Extract inline object types to interfaces
// Consider creating interfaces for complex object types
import React from 'react';

import { FacilityCard } from '@/data/consultingRooms/consultingRoomsData';

interface FacilitiesSectionProps {
  title: string;
  facilities: FacilityCard[];
  locationTitle: string;
  locationDescription: string;
  locationAddress: string;
  mainImage: {
    src: string;
    alt: string;
  };
}

const FacilitiesSection: React.FC<FacilitiesSectionProps> = ({
  title,
  facilities,
  locationTitle,
  locationDescription,
  locationAddress,
  mainImage
}) => {
  return (
    <div>
      <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{title}</h2>

      <div className="space-y-6 mb-8">
        {facilities.map((facility) => (
          <div key={facility.id} className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">{facility.title}</h3>
            <p className="text-foreground/90">{facility.description}</p>
          </div>
        ))}

        <div className="card p-6 rounded-lg shadow-md medical-card">
          <h3 className="text-xl font-semibold mb-3 text-primary">{locationTitle}</h3>
          <p className="text-foreground/90 mb-4">{locationDescription}</p>
          <p className="text-foreground/90">
            <strong>{locationAddress}</strong>
          </p>
        </div>
      </div>

      <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl">
        <img
          src={mainImage.src}
          alt={mainImage.alt}
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
};

FacilitiesSection.displayName = 'FacilitiesSection';

export default FacilitiesSection;
