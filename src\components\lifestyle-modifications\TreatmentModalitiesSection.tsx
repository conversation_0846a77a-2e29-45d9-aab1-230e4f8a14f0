import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { TreatmentModality } from '@/data/lifestyle-modifications/treatmentModalitiesData';

interface TreatmentModalitiesSectionProps {
  modalities: TreatmentModality[];
  title: string;
  description: string;
}

const TreatmentModalitiesSection: React.FC<TreatmentModalitiesSectionProps> = ({
  modalities, 
  title, 
  description 
}) => {
  const modalitiesWithImages = modalities.filter(modality => modality.imageSrc);
  const modalitiesWithoutImages = modalities.filter(modality => !modality.imageSrc);

  return (
    <section className="py-16">
      <div className="container max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{title}</h2>
          <p className="text-foreground/90 max-w-3xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        {/* Modalities with images */}
        {modalitiesWithImages.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {modalitiesWithImages.map((modality, index) => (
              <Card 
                key={modality.id} 
                className="medical-card/50 backdrop-blur-sm border border-border/50 shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in" 
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="relative h-48 overflow-hidden rounded-t-lg">
                  <SafeImage
                    src={modality.imageSrc!}
                    alt={modality.imageAlt!}
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/patient-resources/treatment-default.jpg"
                  />
                </div>
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg text-foreground">{modality.title}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0 space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2 text-foreground">Benefits:</h4>
                    <ul className="space-y-1">
                      {modality.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-start text-sm">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-foreground/90">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-foreground">Considerations:</h4>
                    <ul className="space-y-1">
                      {modality.considerations.map((consideration, idx) => (
                        <li key={idx} className="flex items-start text-sm">
                          <span className="text-info mr-2 mt-0.5">•</span>
                          <span className="text-foreground/90">{consideration}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button asChild variant="outline" className="w-full">
                    <Link to={modality.learnMoreLink} className="text-primary hover:">Learn More</Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}

        {/* Modalities without images */}
        {modalitiesWithoutImages.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {modalitiesWithoutImages.map((modality, index) => (
              <Card 
                key={modality.id} 
                className="medical-card/50 backdrop-blur-sm border border-border/50 shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in" 
                style={{ animationDelay: `${(modalitiesWithImages.length + index) * 100}ms` }}
              >
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg text-foreground">{modality.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2 text-foreground">Benefits:</h4>
                    <ul className="space-y-1">
                      {modality.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-start text-sm">
                          <span className="text-success mr-2 mt-0.5">✓</span>
                          <span className="text-foreground/90">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-foreground">Considerations:</h4>
                    <ul className="space-y-1">
                      {modality.considerations.map((consideration, idx) => (
                        <li key={idx} className="flex items-start text-sm">
                          <span className="text-info mr-2 mt-0.5">•</span>
                          <span className="text-foreground/90">{consideration}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button asChild variant="outline" className="w-full">
                    <Link to={modality.learnMoreLink} className="text-primary hover:">Learn More</Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

TreatmentModalitiesSection.displayName = 'TreatmentModalitiesSection';

export default TreatmentModalitiesSection;
